import React, { useState } from 'react';
import { cn } from '@/lib/utils';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width: number;
  height: number;
  className?: string;
  priority?: boolean;
  quality?: number;
  fallback?: string;
  onLoad?: () => void;
  onError?: () => void;
}

/**
 * Componente de imagem otimizada com suporte a WebP e fallback
 * Inclui width/height explícitas para evitar layout shift
 */
export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  className,
  priority = false,
  quality = 85,
  fallback = '/faviconx.webp',
  onLoad,
  onError,
  ...props
}) => {
  const [hasError, setHasError] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);

  // Gerar URL WebP otimizada se for do Supabase
  const getOptimizedSrc = (originalSrc: string) => {
    if (originalSrc.includes('supabase.co')) {
      // Adicionar parâmetros de otimização do Supabase
      const url = new URL(originalSrc);
      url.searchParams.set('width', width.toString());
      url.searchParams.set('height', height.toString());
      url.searchParams.set('quality', quality.toString());
      url.searchParams.set('format', 'webp');
      return url.toString();
    }
    return originalSrc;
  };

  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };

  const handleError = () => {
    setHasError(true);
    onError?.();
  };

  const optimizedSrc = hasError ? fallback : getOptimizedSrc(src);

  return (
    <div 
      className={cn("relative overflow-hidden", className)}
      style={{ width, height }}
    >
      {/* Skeleton loader */}
      {!isLoaded && !hasError && (
        <div 
          className="absolute inset-0 bg-gray-200 dark:bg-gray-700 animate-pulse"
          style={{ width, height }}
        />
      )}
      
      {/* Imagem principal */}
      <img
        src={optimizedSrc}
        alt={alt}
        width={width}
        height={height}
        className={cn(
          "transition-opacity duration-300",
          isLoaded ? "opacity-100" : "opacity-0",
          className
        )}
        loading={priority ? "eager" : "lazy"}
        decoding={priority ? "sync" : "async"}
        fetchpriority={priority ? "high" : "auto"}
        onLoad={handleLoad}
        onError={handleError}
        {...props}
      />
    </div>
  );
};

/**
 * Componente para imagens críticas (carregamento prioritário)
 */
export const PriorityOptimizedImage: React.FC<Omit<OptimizedImageProps, 'priority'>> = (props) => {
  return <OptimizedImage {...props} priority={true} />;
};

/**
 * Componente para imagens de avatar/perfil
 */
export const AvatarOptimizedImage: React.FC<Omit<OptimizedImageProps, 'width' | 'height'> & { size: number }> = ({ 
  size, 
  ...props 
}) => {
  return (
    <OptimizedImage 
      {...props} 
      width={size} 
      height={size} 
      className={cn("rounded-full", props.className)}
    />
  );
};

/**
 * Componente para logos
 */
export const LogoOptimizedImage: React.FC<Omit<OptimizedImageProps, 'priority' | 'quality'>> = (props) => {
  return (
    <OptimizedImage 
      {...props} 
      priority={true} 
      quality={95}
      className={cn("object-contain", props.className)}
    />
  );
};
