import HelmetWrapper from "@/components/utils/HelmetWrapper";

interface ChildcareSEOProps {
  title: string;
  description: string;
  slug: string;
  category: 'overview' | 'growth' | 'vaccines' | 'formulas' | 'supplementation' | 'development' | 'main';
  keywords: string[];
  clinicalUse: string;
  targetAge: string;
  features: string[];
  benefits: string[];
  clinicalSignificance: string;
  relatedTopics: string[];
}

export const ChildcareSEO = ({
  title,
  description,
  slug,
  category,
  keywords,
  clinicalUse,
  targetAge,
  features,
  benefits,
  clinicalSignificance,
  relatedTopics
}: ChildcareSEOProps) => {

  // Gerar título dinâmico otimizado
  const generateTitle = () => {
    let seoTitle = `${title}`;
    
    if (category === 'overview') {
      seoTitle += ` - Acompanhamento Pediátrico`;
    } else if (category === 'growth') {
      seoTitle += ` - Crescimento Infantil`;
    } else if (category === 'vaccines') {
      seoTitle += ` - Calendário Vacinal`;
    } else if (category === 'formulas') {
      seoTitle += ` - Nutrição Infantil`;
    } else if (category === 'supplementation') {
      seoTitle += ` - Suplementação Pediátrica`;
    } else if (category === 'development') {
      seoTitle += ` - Desenvolvimento Neuropsicomotor`;
    } else if (category === 'main') {
      seoTitle += ` - Cuidados Pediátricos`;
    }
    
    seoTitle += ` | PedBook`;
    
    return seoTitle.substring(0, 60);
  };

  // Gerar descrição dinâmica
  const generateDescription = () => {
    let desc = `${title}: ${description}`;
    
    if (targetAge) {
      desc += ` Indicado para ${targetAge}.`;
    }
    
    desc += ` ${clinicalUse}`;
    
    if (features.length > 0) {
      desc += ` Inclui: ${features.slice(0, 3).join(', ')}.`;
    }
    
    desc += ` Ferramenta essencial para pediatras e profissionais da saúde.`;
    
    return desc.substring(0, 160);
  };

  // Gerar keywords dinâmicas
  const generateKeywords = () => {
    const baseKeywords = [
      `${title.toLowerCase()}`,
      `${title.toLowerCase()} pediatria`,
      `${title.toLowerCase()} puericultura`,
      `${title.toLowerCase()} criança`,
      `${title.toLowerCase()} infantil`,
      `puericultura ${title.toLowerCase()}`
    ];

    // Adicionar keywords por categoria
    if (category === 'overview') {
      baseKeywords.push(
        'acompanhamento pediátrico',
        'consulta puericultura',
        'desenvolvimento infantil',
        'crescimento criança',
        'avaliação pediátrica'
      );
    }

    if (category === 'growth') {
      baseKeywords.push(
        'curvas crescimento',
        'percentil crescimento',
        'peso altura criança',
        'crescimento infantil',
        'desenvolvimento físico'
      );
    }

    if (category === 'vaccines') {
      baseKeywords.push(
        'calendário vacinal',
        'vacinas criança',
        'imunização infantil',
        'esquema vacinal',
        'vacinação pediátrica'
      );
    }

    if (category === 'formulas') {
      baseKeywords.push(
        'fórmulas infantis',
        'leite artificial',
        'nutrição infantil',
        'alimentação bebê',
        'fórmula láctea'
      );
    }

    if (category === 'supplementation') {
      baseKeywords.push(
        'suplementação infantil',
        'vitaminas criança',
        'ferro infantil',
        'vitamina d criança',
        'suplementos pediátricos'
      );
    }

    if (category === 'development') {
      baseKeywords.push(
        'desenvolvimento neuropsicomotor',
        'marcos desenvolvimento',
        'dnpm',
        'desenvolvimento motor',
        'desenvolvimento cognitivo'
      );
    }

    // Adicionar keywords específicas passadas
    baseKeywords.push(...keywords);

    // Adicionar features como keywords
    features.forEach(feature => {
      baseKeywords.push(`${feature.toLowerCase()}`);
    });

    // Adicionar tópicos relacionados
    relatedTopics.forEach(topic => {
      baseKeywords.push(`${topic.toLowerCase()}`);
    });

    return baseKeywords.join(", ");
  };

  // URL canônica
  const canonicalUrl = slug === 'main' 
    ? `https://pedb.com.br/puericultura`
    : `https://pedb.com.br/puericultura/${slug}`;

  // Schema.org para serviço médico
  const medicalServiceSchema = {
    "@context": "https://schema.org",
    "@type": "MedicalWebPage",
    "name": generateTitle(),
    "description": generateDescription(),
    "url": canonicalUrl,
    "mainContentOfPage": {
      "@type": "WebPageElement",
      "cssSelector": "main"
    },
    "specialty": "Pediatria",
    "audience": {
      "@type": "MedicalAudience",
      "audienceType": "Médicos pediatras e profissionais da saúde"
    },
    "about": {
      "@type": "MedicalCondition",
      "name": title,
      "description": clinicalUse
    },
    "lastReviewed": new Date().toISOString().split('T')[0],
    "reviewedBy": {
      "@type": "Organization",
      "name": "PedBook",
      "url": "https://pedb.com.br"
    }
  };

  // Schema.org para organização médica
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "MedicalOrganization",
    "name": "PedBook",
    "url": "https://pedb.com.br",
    "logo": {
      "@type": "ImageObject",
      "url": "https://pedb.com.br/faviconx.webp"
    },
    "medicalSpecialty": "Pediatria",
    "serviceType": "Puericultura",
    "areaServed": "Brasil",
    "availableService": {
      "@type": "MedicalTherapy",
      "name": title,
      "description": clinicalUse
    }
  };

  // Breadcrumb Schema
  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "PedBook",
        "item": "https://pedb.com.br"
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Puericultura",
        "item": "https://pedb.com.br/puericultura"
      }
    ]
  };

  // Adicionar item específico se não for a página principal
  if (slug !== 'main') {
    breadcrumbSchema.itemListElement.push({
      "@type": "ListItem",
      "position": 3,
      "name": title,
      "item": canonicalUrl
    });
  }

  return (
    <HelmetWrapper>
      {/* Título e Descrição Dinâmicos */}
      <title>{generateTitle()}</title>
      <meta name="description" content={generateDescription()} />
      <meta name="keywords" content={generateKeywords()} />

      {/* Meta tags médicas específicas */}
      <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1" />
      <meta name="googlebot" content="index, follow" />
      <meta name="medical-content" content="pediatric-care" />
      <meta name="target-audience" content="healthcare-professionals" />
      <meta name="content-type" content="medical-information" />
      <meta name="clinical-specialty" content="pediatrics" />
      <meta name="care-type" content={category} />
      
      {/* Geo targeting */}
      <meta name="geo.region" content="BR" />
      <meta name="geo.country" content="Brazil" />
      <meta name="language" content="Portuguese" />

      {/* Open Graph */}
      <meta property="og:title" content={generateTitle()} />
      <meta property="og:description" content={generateDescription()} />
      <meta property="og:type" content="website" />
      <meta property="og:url" content={canonicalUrl} />
      <meta property="og:image" content="https://pedb.com.br/faviconx.webp" />
      <meta property="og:image:alt" content={`${title} - PedBook`} />
      <meta property="og:site_name" content="PedBook" />
      <meta property="og:locale" content="pt_BR" />
      <meta property="article:section" content="Medicina" />
      <meta property="article:tag" content="Puericultura" />
      <meta property="article:tag" content="Pediatria" />
      <meta property="article:tag" content={category} />
      {relatedTopics.map((topic, index) => (
        <meta key={index} property="article:tag" content={topic} />
      ))}

      {/* Twitter Cards */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={generateTitle()} />
      <meta name="twitter:description" content={generateDescription()} />
      <meta name="twitter:image" content="https://pedb.com.br/faviconx.webp" />
      <meta name="twitter:site" content="@pedbook" />

      {/* Canonical */}
      <link rel="canonical" href={canonicalUrl} />

      {/* Schema.org - Página Médica */}
      <script type="application/ld+json">
        {JSON.stringify(medicalServiceSchema)}
      </script>

      {/* Schema.org - Organização Médica */}
      <script type="application/ld+json">
        {JSON.stringify(organizationSchema)}
      </script>

      {/* Schema.org - Breadcrumb */}
      <script type="application/ld+json">
        {JSON.stringify(breadcrumbSchema)}
      </script>
    </HelmetWrapper>
  );
};
