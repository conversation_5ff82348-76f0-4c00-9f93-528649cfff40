import HelmetWrapper from "@/components/utils/HelmetWrapper";

interface FlowchartSEOProps {
  title: string;
  description: string;
  slug: string;
  category?: 'emergency' | 'venomous' | 'general';
  subcategory?: string; // Para animais peçonhentos
  keywords?: string[];
  clinicalCondition: string;
  targetAge?: 'pediatric' | 'all';
  urgencyLevel?: 'emergency' | 'urgent' | 'routine';
  symptoms?: string[];
  treatment?: string[];
  parentPath?: string; // Para breadcrumbs aninhados
}

export const FlowchartSEO = ({
  title,
  description,
  slug,
  category = 'general',
  subcategory,
  keywords = [],
  clinicalCondition,
  targetAge = 'pediatric',
  urgencyLevel = 'routine',
  symptoms = [],
  treatment = [],
  parentPath
}: FlowchartSEOProps) => {

  // Gerar título dinâmico otimizado
  const generateTitle = () => {
    let seoTitle = `${title}`;
    
    if (category === 'venomous') {
      seoTitle += ` - Protocolo Pediátrico`;
    } else if (urgencyLevel === 'emergency') {
      seoTitle += ` - Emergência Pediátrica`;
    } else {
      seoTitle += ` - Fluxograma Pediátrico`;
    }
    
    seoTitle += ` | PedBook`;
    
    return seoTitle.substring(0, 60);
  };

  // Gerar descrição dinâmica
  const generateDescription = () => {
    let desc = `Fluxograma interativo para manejo de ${title.toLowerCase()}`;
    
    if (targetAge === 'pediatric') {
      desc += ` em pediatria`;
    }
    
    desc += `. ${description}`;
    
    if (urgencyLevel === 'emergency') {
      desc += ` Protocolo de emergência com algoritmo passo a passo.`;
    } else if (category === 'venomous') {
      desc += ` Protocolo de atendimento com doses e antídotos específicos.`;
    } else {
      desc += ` Algoritmo clínico baseado em evidências.`;
    }
    
    return desc.substring(0, 160);
  };

  // Gerar keywords dinâmicas
  const generateKeywords = () => {
    const baseKeywords = [
      `fluxograma ${title.toLowerCase()}`,
      `protocolo ${title.toLowerCase()}`,
      `manejo ${title.toLowerCase()}`,
      `algoritmo ${title.toLowerCase()}`
    ];

    if (targetAge === 'pediatric') {
      baseKeywords.push(
        `${title.toLowerCase()} pediatria`,
        `${title.toLowerCase()} criança`,
        `${title.toLowerCase()} pediátrico`,
        `protocolo ${title.toLowerCase()} pediatria`
      );
    }

    if (urgencyLevel === 'emergency') {
      baseKeywords.push(
        `emergência ${title.toLowerCase()}`,
        `urgência ${title.toLowerCase()}`,
        `pronto socorro ${title.toLowerCase()}`
      );
    }

    if (category === 'venomous') {
      baseKeywords.push(
        `acidente ${title.toLowerCase()}`,
        `picada ${title.toLowerCase()}`,
        `envenenamento ${title.toLowerCase()}`,
        `antídoto ${title.toLowerCase()}`
      );
    }

    // Adicionar keywords específicas passadas
    baseKeywords.push(...keywords);

    // Adicionar sintomas como keywords
    symptoms.forEach(symptom => {
      baseKeywords.push(`${symptom} ${title.toLowerCase()}`);
    });

    // Adicionar tratamentos como keywords
    treatment.forEach(treat => {
      baseKeywords.push(`${treat} ${title.toLowerCase()}`);
    });

    return baseKeywords.join(", ");
  };

  // URL canônica
  const canonicalUrl = parentPath 
    ? `https://pedb.com.br/flowcharts/${parentPath}/${slug}`
    : `https://pedb.com.br/flowcharts/${slug}`;

  // Schema.org para procedimento médico
  const medicalProcedureSchema = {
    "@context": "https://schema.org",
    "@type": "MedicalProcedure",
    "name": `Fluxograma ${title} Pediátrico`,
    "description": generateDescription(),
    "url": canonicalUrl,
    "procedureType": category === 'venomous' ? 'ToxicologyProcedure' : 
                    urgencyLevel === 'emergency' ? 'EmergencyProcedure' : 'MedicalProcedure',
    "bodyLocation": clinicalCondition,
    "preparation": "Avaliação inicial do paciente",
    "followup": "Monitoramento contínuo conforme protocolo",
    "indication": symptoms.length > 0 ? symptoms.join(', ') : `Suspeita de ${title.toLowerCase()}`,
    "contraindication": "Avaliar contraindicações específicas",
    "audience": {
      "@type": "MedicalAudience",
      "audienceType": "Profissionais de saúde pediátrica"
    },
    "about": {
      "@type": "MedicalCondition",
      "name": title,
      "associatedAnatomy": clinicalCondition
    },
    "publisher": {
      "@type": "Organization",
      "name": "PedBook",
      "url": "https://pedb.com.br",
      "logo": {
        "@type": "ImageObject",
        "url": "https://pedb.com.br/faviconx.webp"
      }
    }
  };

  // Schema.org para página médica
  const medicalPageSchema = {
    "@context": "https://schema.org",
    "@type": "MedicalWebPage",
    "name": generateTitle(),
    "description": generateDescription(),
    "url": canonicalUrl,
    "mainContentOfPage": {
      "@type": "WebPageElement",
      "cssSelector": "main"
    },
    "specialty": "Pediatria",
    "audience": {
      "@type": "MedicalAudience",
      "audienceType": "Médicos pediatras e profissionais da saúde"
    },
    "about": {
      "@type": "MedicalProcedure",
      "name": title
    },
    "lastReviewed": new Date().toISOString().split('T')[0],
    "reviewedBy": {
      "@type": "Organization",
      "name": "PedBook",
      "url": "https://pedb.com.br"
    }
  };

  // Breadcrumb Schema
  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "PedBook",
        "item": "https://pedb.com.br"
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Fluxogramas",
        "item": "https://pedb.com.br/flowcharts"
      },
      ...(parentPath ? [{
        "@type": "ListItem",
        "position": 3,
        "name": subcategory || "Categoria",
        "item": `https://pedb.com.br/flowcharts/${parentPath}`
      }] : []),
      {
        "@type": "ListItem",
        "position": parentPath ? 4 : 3,
        "name": title,
        "item": canonicalUrl
      }
    ]
  };

  return (
    <HelmetWrapper>
      {/* Título e Descrição Dinâmicos */}
      <title>{generateTitle()}</title>
      <meta name="description" content={generateDescription()} />
      <meta name="keywords" content={generateKeywords()} />

      {/* Meta tags médicas específicas */}
      <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1" />
      <meta name="googlebot" content="index, follow" />
      <meta name="medical-content" content="clinical-protocol" />
      <meta name="target-audience" content="healthcare-professionals" />
      <meta name="content-type" content="medical-flowchart" />
      <meta name="clinical-specialty" content="pediatrics" />
      <meta name="urgency-level" content={urgencyLevel} />
      
      {/* Geo targeting */}
      <meta name="geo.region" content="BR" />
      <meta name="geo.country" content="Brazil" />
      <meta name="language" content="Portuguese" />

      {/* Open Graph */}
      <meta property="og:title" content={generateTitle()} />
      <meta property="og:description" content={generateDescription()} />
      <meta property="og:type" content="article" />
      <meta property="og:url" content={canonicalUrl} />
      <meta property="og:image" content="https://pedb.com.br/faviconx.webp" />
      <meta property="og:image:alt" content={`Fluxograma ${title} - PedBook`} />
      <meta property="og:site_name" content="PedBook" />
      <meta property="og:locale" content="pt_BR" />
      <meta property="article:section" content="Medicina" />
      <meta property="article:tag" content="Pediatria" />
      <meta property="article:tag" content="Fluxogramas" />
      <meta property="article:tag" content="Protocolos" />
      <meta property="article:tag" content={clinicalCondition} />
      {category && <meta property="article:tag" content={category} />}

      {/* Twitter Cards */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={generateTitle()} />
      <meta name="twitter:description" content={generateDescription()} />
      <meta name="twitter:image" content="https://pedb.com.br/faviconx.webp" />
      <meta name="twitter:site" content="@pedbook" />

      {/* Canonical */}
      <link rel="canonical" href={canonicalUrl} />

      {/* Schema.org - Procedimento Médico */}
      <script type="application/ld+json">
        {JSON.stringify(medicalProcedureSchema)}
      </script>

      {/* Schema.org - Página Médica */}
      <script type="application/ld+json">
        {JSON.stringify(medicalPageSchema)}
      </script>

      {/* Schema.org - Breadcrumb */}
      <script type="application/ld+json">
        {JSON.stringify(breadcrumbSchema)}
      </script>
    </HelmetWrapper>
  );
};
