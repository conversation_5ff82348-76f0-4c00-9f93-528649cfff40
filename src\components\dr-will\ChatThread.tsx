
import React from 'react';
import { MessageList } from './MessageList';
import { ChatInput } from './ChatInput';
import { Message } from '@/types/chat';
import { motion } from 'framer-motion';
import { EmptyState } from './EmptyState';

interface ChatThreadProps {
  messages: Message[];
  isLoading: boolean;
  onSendMessage: (content: string, imageUrl?: string | string[]) => void;
  showWelcomeScreen?: boolean;
}

// Adicionar a interface Window para permitir adicionar propriedades globais
declare global {
  interface Window {
    ChatThreadComponent?: {
      resetInteraction: () => void;
    };
  }
}

export const ChatThread: React.FC<ChatThreadProps> = ({
  messages,
  isLoading,
  onSendMessage,
  showWelcomeScreen = false,
}) => {


  // Usar um estado para rastrear se o usuário já interagiu com o chat
  const [hasInteracted, setHasInteracted] = React.useState(false);

  // Estado local para armazenar mensagens temporárias (para garantir que as mensagens do usuário sejam exibidas imediatamente)
  const [localMessages, setLocalMessages] = React.useState<Message[]>([]);

  // Expor um método para resetar o estado de interação
  React.useEffect(() => {
    // Adicionar o método ao objeto window para que possa ser chamado de fora
    window.ChatThreadComponent = {
      resetInteraction: () => {

        setHasInteracted(false);
        setLocalMessages([]);
      }
    };

    // Limpar ao desmontar
    return () => {
      window.ChatThreadComponent = undefined;
    };
  }, []);

  // Efeito para limpar o estado local quando showWelcomeScreen mudar para true
  React.useEffect(() => {
    if (showWelcomeScreen) {

      setHasInteracted(false);
      setLocalMessages([]);
    }
  }, [showWelcomeScreen]);

  // Efeito para limpar o estado local quando as mensagens mudarem completamente
  // Isso acontece quando o usuário muda de thread
  React.useEffect(() => {
    // CORREÇÃO CRÍTICA: Sempre limpar localMessages quando messages mudar
    // Isso garante que não tenhamos mensagens de threads diferentes
    setLocalMessages([]);
  }, [messages]);

  // Atualizar o estado local quando as mensagens do prop mudarem
  React.useEffect(() => {
    // CORREÇÃO: Sempre sincronizar localMessages com messages
    setLocalMessages(messages);
  }, [messages]);

  // Não usamos mais mensagens de carregamento vazias, em vez disso, usamos o componente TypingIndicator
  // que é renderizado diretamente no MessageList quando isLoading é true
  React.useEffect(() => {
    if (!isLoading) {
      // Remover mensagem de carregamento quando isLoading for false
      setLocalMessages(prev => prev.filter(msg => !msg.isLoading));
    }
  }, [isLoading]);

  // Verificar se há mensagens ou se o usuário está digitando
  const hasMessages = messages.length > 0 || localMessages.length > 0;
  const isUserInteracting = isLoading || hasInteracted;

  // Determinar se devemos mostrar a lista de mensagens ou a tela de boas-vindas
  // Se showWelcomeScreen for true, forçamos a exibição da tela de boas-vindas
  const shouldShowMessageList = !showWelcomeScreen && (hasMessages || isUserInteracting);





  // Função para enviar mensagem e marcar que houve interação
  const handleSendMessage = (content: string, imageUrl?: string | string[]) => {
    setHasInteracted(true);

    // Obter o threadId atual das mensagens, se houver
    const currentThreadId = messages.length > 0 ? messages[0].threadId : undefined;

    // Adicionar a mensagem do usuário ao estado local imediatamente
    const userMessage: Message = {
      role: 'user',
      content,
      timestamp: new Date(),
      image_url: imageUrl,
      threadId: currentThreadId, // Adicionar o threadId atual
    };


    setLocalMessages(prev => [...prev, userMessage]);

    // Chamar a função original
    onSendMessage(content, imageUrl);
  };

  // Combinar mensagens do prop com mensagens locais, removendo duplicatas
  const combinedMessages = React.useMemo(() => {
    // Filtrar mensagens vazias do assistente
    const filterEmptyMessages = (msgs: Message[]) => {
      return msgs.filter(msg => {
        // Remover mensagens vazias do assistente
        if (msg.role === 'assistant' && (!msg.content || msg.content.trim() === '')) {
          return false;
        }
        return true;
      });
    };

    // Filtrar mensagens vazias
    const filteredMessages = filterEmptyMessages(messages);
    const filteredLocalMessages = filterEmptyMessages(localMessages);

    // Se não houver mensagens locais, use as mensagens do prop
    if (filteredLocalMessages.length === 0) {
      return filteredMessages;
    }

    // Se não houver mensagens do prop, use as mensagens locais
    if (filteredMessages.length === 0) {
      return filteredLocalMessages;
    }

    // Obter o threadId atual das mensagens, se houver
    const currentThreadId = filteredMessages.length > 0 ? filteredMessages[0].threadId : undefined;

    // Combinar as mensagens, removendo duplicatas
    const combined = [...filteredMessages];

    // Adicionar apenas mensagens locais que pertencem ao mesmo thread ou não têm threadId
    // E que não estão nas mensagens do prop
    filteredLocalMessages.forEach(localMsg => {
      // Verificar se a mensagem local pertence ao thread atual ou não tem threadId
      const isSameThread = !localMsg.threadId || localMsg.threadId === currentThreadId;

      if (isSameThread) {
        const exists = combined.some(
          msg => msg.role === localMsg.role &&
                 msg.content === localMsg.content &&
                 Math.abs(msg.timestamp.getTime() - localMsg.timestamp.getTime()) < 1000
        );

        if (!exists) {
          combined.push(localMsg);
        }
      }
    });

    // Ordenar por timestamp
    return combined.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  }, [messages, localMessages]);

  return (
    <div className="flex flex-col h-full overflow-hidden bg-white dark:bg-slate-900">
      {showWelcomeScreen || !shouldShowMessageList ? (
        <EmptyState onSendMessage={handleSendMessage} />
      ) : (
        <MessageList
          messages={combinedMessages}
          isLoading={isLoading}
          onSendMessage={handleSendMessage}
        />
      )}
      <ChatInput onSendMessage={handleSendMessage} isLoading={isLoading} />
    </div>
  );
};
