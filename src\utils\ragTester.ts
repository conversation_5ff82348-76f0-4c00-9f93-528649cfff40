import { RAGService } from '@/services/ragService';

/**
 * Função para testar o sistema RAG
 * Útil para desenvolvimento e debug
 */
export const testRAG = async () => {
  console.log('🧪 Testando sistema RAG...');

  // Exemplos de respostas que mencionam medicamentos
  const testResponses = [
    "Para o tratamento da febre, você pode usar paracetamol na dose de 10-15mg/kg a cada 6 horas.",
    "O dipirona é uma boa opção para analgesia em crianças, sempre respeitando a dosagem adequada.",
    "Amoxicilina é o antibiótico de primeira escolha para otite média aguda em pediatria.",
    "Esta resposta não menciona nenhum medicamento específico.",
  ];

  for (let i = 0; i < testResponses.length; i++) {
    console.log(`\n--- Teste ${i + 1} ---`);
    console.log('Resposta original:', testResponses[i]);
    
    try {
      const result = await RAGService.enhanceResponse(testResponses[i]);
      console.log('Medicamentos encontrados:', result.medications.map(m => m.name));
      console.log('Resposta melhorada:', result.enhancedResponse);
    } catch (error) {
      console.error('Erro no teste:', error);
    }
  }
};

/**
 * Função para testar busca de medicamentos
 */
export const testMedicationSearch = async (query: string) => {
  console.log(`🔍 Testando busca por: "${query}"`);
  
  try {
    const results = await RAGService.searchMedications(query);
    console.log('Resultados encontrados:', results);
    return results;
  } catch (error) {
    console.error('Erro na busca:', error);
    return [];
  }
};

// Exportar para uso no console do navegador
if (typeof window !== 'undefined') {
  (window as any).testRAG = testRAG;
  (window as any).testMedicationSearch = testMedicationSearch;
  (window as any).RAGService = RAGService;
}
