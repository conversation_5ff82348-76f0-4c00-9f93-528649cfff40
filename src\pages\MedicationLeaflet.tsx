import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, ScrollText, Building, Pill, AlertTriangle, Info, Clock, Shield } from 'lucide-react';
import { Helmet } from 'react-helmet-async';

interface MedicationLeaflet {
  id: string;
  title: string;
  content: string;
  manufacturer: string;
  registration_number: string;
  active_ingredient: string;
  therapeutic_class: string;
  presentation: string;
  indications: string;
  contraindications: string;
  warnings: string;
  adverse_reactions: string;
  interactions: string;
  posology: string;
  overdose: string;
  storage: string;
  pdf_url?: string;
  anvisa_url?: string;
  last_updated: string;
  medication_name: string;
}

const MedicationLeaflet: React.FC = () => {
  const { medicationId } = useParams<{ medicationId: string }>();
  const navigate = useNavigate();

  const { data: leaflet, isLoading, error } = useQuery({
    queryKey: ['medication-leaflet', medicationId],
    queryFn: async (): Promise<MedicationLeaflet> => {
      if (!medicationId) throw new Error('ID do medicamento não fornecido');

      const { data, error } = await supabase
        .from('pedbook_medication_leaflets')
        .select(`
          *,
          pedbook_medications!inner(name)
        `)
        .eq('medication_id', medicationId)
        .single();

      if (error) throw error;
      
      return {
        ...data,
        medication_name: data.pedbook_medications.name
      };
    },
    enabled: !!medicationId,
    staleTime: 30 * 60 * 1000, // 30 minutos
    retry: 1
  });

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !leaflet) {
    return (
      <div className="container mx-auto px-4 py-6">
        <Card>
          <CardContent className="p-6 text-center">
            <AlertTriangle className="h-12 w-12 text-amber-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Bula não encontrada</h2>
            <p className="text-muted-foreground mb-4">
              Não foi possível encontrar a bula para este medicamento.
            </p>
            <Button onClick={() => navigate(-1)}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Voltar
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6 space-y-6">
      <Helmet>
        <title>Bula - {leaflet.medication_name} | PedBook</title>
        <meta name="description" content={`Bula completa do medicamento ${leaflet.medication_name} - ${leaflet.active_ingredient}`} />
      </Helmet>

      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" onClick={() => navigate(-1)}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Voltar
        </Button>
        <div className="flex items-center gap-2">
          <ScrollText className="h-6 w-6 text-orange-500" />
          <h1 className="text-2xl font-bold">Bula do Medicamento</h1>
        </div>
      </div>

      {/* Informações básicas */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Pill className="h-5 w-5 text-primary" />
            {leaflet.title}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="font-semibold text-sm text-muted-foreground">Princípio Ativo</h3>
              <p>{leaflet.active_ingredient}</p>
            </div>
            <div>
              <h3 className="font-semibold text-sm text-muted-foreground">Classe Terapêutica</h3>
              <p>{leaflet.therapeutic_class}</p>
            </div>
            <div>
              <h3 className="font-semibold text-sm text-muted-foreground">Fabricante</h3>
              <p className="flex items-center gap-2">
                <Building className="h-4 w-4" />
                {leaflet.manufacturer}
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-sm text-muted-foreground">Registro ANVISA</h3>
              <p>{leaflet.registration_number}</p>
            </div>
          </div>
          
          {leaflet.presentation && (
            <div>
              <h3 className="font-semibold text-sm text-muted-foreground">Apresentação</h3>
              <p>{leaflet.presentation}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Seções da bula */}
      <div className="space-y-4">
        {leaflet.indications && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-green-600">
                <Info className="h-5 w-5" />
                Indicações
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="whitespace-pre-wrap">{leaflet.indications}</p>
            </CardContent>
          </Card>
        )}

        {leaflet.contraindications && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-red-600">
                <AlertTriangle className="h-5 w-5" />
                Contraindicações
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="whitespace-pre-wrap">{leaflet.contraindications}</p>
            </CardContent>
          </Card>
        )}

        {leaflet.posology && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-blue-600">
                <Clock className="h-5 w-5" />
                Posologia
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="whitespace-pre-wrap">{leaflet.posology}</p>
            </CardContent>
          </Card>
        )}

        {leaflet.warnings && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-amber-600">
                <Shield className="h-5 w-5" />
                Advertências e Precauções
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="whitespace-pre-wrap">{leaflet.warnings}</p>
            </CardContent>
          </Card>
        )}

        {leaflet.adverse_reactions && (
          <Card>
            <CardHeader>
              <CardTitle className="text-orange-600">Reações Adversas</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="whitespace-pre-wrap">{leaflet.adverse_reactions}</p>
            </CardContent>
          </Card>
        )}

        {leaflet.interactions && (
          <Card>
            <CardHeader>
              <CardTitle className="text-purple-600">Interações Medicamentosas</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="whitespace-pre-wrap">{leaflet.interactions}</p>
            </CardContent>
          </Card>
        )}

        {leaflet.overdose && (
          <Card>
            <CardHeader>
              <CardTitle className="text-red-600">Superdosagem</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="whitespace-pre-wrap">{leaflet.overdose}</p>
            </CardContent>
          </Card>
        )}

        {leaflet.storage && (
          <Card>
            <CardHeader>
              <CardTitle className="text-gray-600">Armazenamento</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="whitespace-pre-wrap">{leaflet.storage}</p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Links externos */}
      {(leaflet.pdf_url || leaflet.anvisa_url) && (
        <Card>
          <CardHeader>
            <CardTitle>Links Externos</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {leaflet.pdf_url && (
              <Button variant="outline" asChild>
                <a href={leaflet.pdf_url} target="_blank" rel="noopener noreferrer">
                  Ver PDF Original
                </a>
              </Button>
            )}
            {leaflet.anvisa_url && (
              <Button variant="outline" asChild>
                <a href={leaflet.anvisa_url} target="_blank" rel="noopener noreferrer">
                  Ver na ANVISA
                </a>
              </Button>
            )}
          </CardContent>
        </Card>
      )}

      {/* Informações de atualização */}
      <div className="text-center text-sm text-muted-foreground">
        Última atualização: {new Date(leaflet.last_updated).toLocaleDateString('pt-BR')}
      </div>
    </div>
  );
};

export default MedicationLeaflet;
