/**
 * Circuit Breaker para prevenir loops infinitos em WebSockets e Analytics
 * Implementa proteção contra requisições excessivas
 */

interface CircuitBreakerConfig {
  maxFailures: number;
  resetTimeout: number;
  maxRequestsPerMinute: number;
}

class CircuitBreaker {
  private failures: number = 0;
  private lastFailureTime: number = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';
  private requestCounts: Map<string, { count: number; timestamp: number }> = new Map();
  
  constructor(private config: CircuitBreakerConfig) {}

  // Verificar se pode executar a operação
  canExecute(operation: string): boolean {
    this.cleanOldRequests();
    
    // Verificar rate limiting
    if (!this.checkRateLimit(operation)) {
      console.warn(`🚨 [CircuitBreaker] Rate limit excedido para ${operation}`);
      return false;
    }

    // Verificar estado do circuit breaker
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.config.resetTimeout) {
        this.state = 'HALF_OPEN';
        console.log(`🔄 [CircuitBreaker] Tentando reabrir para ${operation}`);
        return true;
      }
      console.warn(`⛔ [CircuitBreaker] Circuito aberto para ${operation}`);
      return false;
    }

    return true;
  }

  // Registrar sucesso
  onSuccess(operation: string): void {
    this.failures = 0;
    this.state = 'CLOSED';
    this.incrementRequestCount(operation);
  }

  // Registrar falha
  onFailure(operation: string): void {
    this.failures++;
    this.lastFailureTime = Date.now();

    if (this.failures >= this.config.maxFailures) {
      this.state = 'OPEN';
      console.error(`🚨 [CircuitBreaker] Circuito aberto para ${operation} após ${this.failures} falhas`);

      // Log crítico do circuit breaker aberto
      import('./problemLogger').then(({ logCircuitBreakerEvent }) => {
        logCircuitBreakerEvent(
          'CircuitBreaker',
          `Circuit aberto para ${operation}`,
          {
            operation,
            failures: this.failures,
            maxFailures: this.config.maxFailures,
            resetTimeout: this.config.resetTimeout,
            timestamp: this.lastFailureTime
          }
        );
      });
    }
  }

  // Verificar rate limiting
  private checkRateLimit(operation: string): boolean {
    const now = Date.now();
    const key = `${operation}_${Math.floor(now / 60000)}`; // Por minuto

    const current = this.requestCounts.get(key) || { count: 0, timestamp: now };

    if (current.count >= this.config.maxRequestsPerMinute) {
      // Log do rate limit para debug
      import('./problemLogger').then(({ logRateLimitEvent }) => {
        logRateLimitEvent(
          'CircuitBreaker',
          this.config.maxRequestsPerMinute,
          current.count,
          { operation, timestamp: now }
        );
      });
      return false;
    }

    return true;
  }

  // Incrementar contador de requisições
  private incrementRequestCount(operation: string): void {
    const now = Date.now();
    const key = `${operation}_${Math.floor(now / 60000)}`;
    
    const current = this.requestCounts.get(key) || { count: 0, timestamp: now };
    current.count++;
    this.requestCounts.set(key, current);
  }

  // Limpar contadores antigos
  private cleanOldRequests(): void {
    const now = Date.now();
    const oneMinuteAgo = now - 60000;
    
    for (const [key, data] of this.requestCounts.entries()) {
      if (data.timestamp < oneMinuteAgo) {
        this.requestCounts.delete(key);
      }
    }
  }

  // Obter status atual
  getStatus(): { state: string; failures: number; requestCounts: number } {
    return {
      state: this.state,
      failures: this.failures,
      requestCounts: this.requestCounts.size
    };
  }
}

// Instâncias globais para diferentes operações
export const analyticsCircuitBreaker = new CircuitBreaker({
  maxFailures: 3,
  resetTimeout: 60000, // 1 minuto
  maxRequestsPerMinute: 10 // Máximo 10 eventos de analytics por minuto
});

export const websocketCircuitBreaker = new CircuitBreaker({
  maxFailures: 2,
  resetTimeout: 300000, // 5 minutos
  maxRequestsPerMinute: 5 // Máximo 5 invalidações por minuto
});

export const apiCircuitBreaker = new CircuitBreaker({
  maxFailures: 5,
  resetTimeout: 30000, // 30 segundos
  maxRequestsPerMinute: 60 // Máximo 60 requests de API por minuto
});

/**
 * Hook para usar circuit breaker em componentes
 */
export const useCircuitBreaker = (type: 'analytics' | 'websocket' | 'api') => {
  const breaker = type === 'analytics' ? analyticsCircuitBreaker :
                  type === 'websocket' ? websocketCircuitBreaker :
                  apiCircuitBreaker;

  const execute = async <T>(operation: string, fn: () => Promise<T>): Promise<T | null> => {
    if (!breaker.canExecute(operation)) {
      return null;
    }

    try {
      const result = await fn();
      breaker.onSuccess(operation);
      return result;
    } catch (error) {
      breaker.onFailure(operation);
      throw error;
    }
  };

  return { execute, getStatus: () => breaker.getStatus() };
};

/**
 * Throttle avançado com circuit breaker integrado
 */
export const createThrottledFunction = <T extends (...args: any[]) => any>(
  fn: T,
  delay: number,
  operation: string,
  breakerType: 'analytics' | 'websocket' | 'api' = 'analytics'
): T => {
  let lastCall = 0;
  const breaker = breakerType === 'analytics' ? analyticsCircuitBreaker :
                 breakerType === 'websocket' ? websocketCircuitBreaker :
                 apiCircuitBreaker;

  return ((...args: Parameters<T>) => {
    const now = Date.now();
    
    if (now - lastCall < delay) {
      console.log(`⏱️ [Throttle] Ignorando chamada de ${operation} (throttled)`);
      return;
    }

    if (!breaker.canExecute(operation)) {
      console.warn(`🚨 [Throttle] Circuit breaker impediu execução de ${operation}`);
      return;
    }

    lastCall = now;
    
    try {
      const result = fn(...args);
      breaker.onSuccess(operation);
      return result;
    } catch (error) {
      breaker.onFailure(operation);
      throw error;
    }
  }) as T;
};
