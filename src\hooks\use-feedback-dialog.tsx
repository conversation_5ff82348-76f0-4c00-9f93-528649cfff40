import React, { createContext, useContext, useState, ReactNode } from 'react';
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { CheckCircle, XCircle, AlertCircle, Info } from "lucide-react";

interface FeedbackDialog {
  isOpen: boolean;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  description: string;
  buttonText?: string;
  onClose?: () => void;
}

interface FeedbackDialogContextType {
  showDialog: (dialog: Omit<FeedbackDialog, 'isOpen'>) => void;
  closeDialog: () => void;
}

const FeedbackDialogContext = createContext<FeedbackDialogContextType | undefined>(undefined);

export const FeedbackDialogProvider = ({ children }: { children: ReactNode }) => {
  const [dialog, setDialog] = useState<FeedbackDialog>({
    isOpen: false,
    type: 'info',
    title: '',
    description: '',
  });

  const showDialog = (newDialog: Omit<FeedbackDialog, 'isOpen'>) => {
    setDialog({
      ...newDialog,
      isOpen: true,
    });
  };

  const closeDialog = () => {
    setDialog(prev => ({ ...prev, isOpen: false }));
    if (dialog.onClose) {
      dialog.onClose();
    }
  };

  const getIcon = () => {
    switch (dialog.type) {
      case 'success':
        return <CheckCircle className="h-6 w-6 text-green-600" />;
      case 'error':
        return <XCircle className="h-6 w-6 text-red-600" />;
      case 'warning':
        return <AlertCircle className="h-6 w-6 text-yellow-600" />;
      case 'info':
      default:
        return <Info className="h-6 w-6 text-blue-600" />;
    }
  };

  const getColorClasses = () => {
    switch (dialog.type) {
      case 'success':
        return 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950';
      case 'error':
        return 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950';
      case 'info':
      default:
        return 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950';
    }
  };

  return (
    <FeedbackDialogContext.Provider value={{ showDialog, closeDialog }}>
      {children}
      
      <Dialog open={dialog.isOpen} onOpenChange={closeDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <div className="flex items-center gap-3 mb-2">
              {getIcon()}
              <DialogTitle className="text-lg">{dialog.title}</DialogTitle>
            </div>
            <DialogDescription className="text-base">
              {dialog.description}
            </DialogDescription>
          </DialogHeader>
          
          {/* Card colorido para destacar o tipo de feedback */}
          <div className={`p-4 rounded-lg border ${getColorClasses()}`}>
            <div className="flex items-center gap-2">
              {getIcon()}
              <span className="text-sm font-medium">
                {dialog.type === 'success' && 'Operação realizada com sucesso!'}
                {dialog.type === 'error' && 'Ocorreu um erro na operação.'}
                {dialog.type === 'warning' && 'Atenção necessária.'}
                {dialog.type === 'info' && 'Informação importante.'}
              </span>
            </div>
          </div>

          <DialogFooter>
            <Button 
              onClick={closeDialog}
              className="w-full"
            >
              {dialog.buttonText || 'Entendi'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </FeedbackDialogContext.Provider>
  );
};

export const useFeedbackDialog = () => {
  const context = useContext(FeedbackDialogContext);
  if (!context) {
    throw new Error('useFeedbackDialog must be used within a FeedbackDialogProvider');
  }
  return context;
};

// Hook de conveniência para diferentes tipos
export const useSuccessDialog = () => {
  const { showDialog } = useFeedbackDialog();
  return (title: string, description: string, buttonText?: string, onClose?: () => void) => {
    showDialog({ type: 'success', title, description, buttonText, onClose });
  };
};

export const useErrorDialog = () => {
  const { showDialog } = useFeedbackDialog();
  return (title: string, description: string, buttonText?: string, onClose?: () => void) => {
    showDialog({ type: 'error', title, description, buttonText, onClose });
  };
};

export const useWarningDialog = () => {
  const { showDialog } = useFeedbackDialog();
  return (title: string, description: string, buttonText?: string, onClose?: () => void) => {
    showDialog({ type: 'warning', title, description, buttonText, onClose });
  };
};

export const useInfoDialog = () => {
  const { showDialog } = useFeedbackDialog();
  return (title: string, description: string, buttonText?: string, onClose?: () => void) => {
    showDialog({ type: 'info', title, description, buttonText, onClose });
  };
};
