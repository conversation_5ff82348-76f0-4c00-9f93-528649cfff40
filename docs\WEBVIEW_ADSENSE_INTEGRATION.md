# 📱 WebView AdSense Integration Guide

## 🚨 Problema
Os anúncios do Google AdSense não abrem links externos corretamente em WebViews Android/iOS.

## ✅ Soluções Implementadas

### 1. **Frontend (JavaScript)**
- ✅ Interceptador de cliques em anúncios
- ✅ Detecção automática de WebView
- ✅ Múltiplos métodos de abertura externa

### 2. **Android WebView (Necessário implementar)**

#### **Método 1: JavaScript Interface**
```java
// No MainActivity.java ou WebViewActivity.java
webView.addJavascriptInterface(new WebAppInterface(this), "Android");

public class WebAppInterface {
    Context mContext;

    WebAppInterface(Context c) {
        mContext = c;
    }

    @JavascriptInterface
    public void openExternalUrl(String url) {
        Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        mContext.startActivity(intent);
    }
}
```

#### **Método 2: WebViewClient Override**
```java
webView.setWebViewClient(new WebViewClient() {
    @Override
    public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
        String url = request.getUrl().toString();
        
        // Detectar URLs de anúncios do Google
        if (url.contains("googleadservices.com") || 
            url.contains("googlesyndication.com") ||
            url.contains("doubleclick.net")) {
            
            // Abrir no navegador externo
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            startActivity(intent);
            return true;
        }
        
        return false;
    }
});
```

#### **Método 3: WebChromeClient (Recomendado)**
```java
webView.setWebChromeClient(new WebChromeClient() {
    @Override
    public boolean onCreateWindow(WebView view, boolean isDialog, 
                                 boolean isUserGesture, Message resultMsg) {
        // Capturar tentativas de abrir nova janela (anúncios)
        WebView newWebView = new WebView(MainActivity.this);
        newWebView.setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                // Abrir no navegador externo
                Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                startActivity(intent);
                return true;
            }
        });
        
        WebView.WebViewTransport transport = (WebView.WebViewTransport) resultMsg.obj;
        transport.setWebView(newWebView);
        resultMsg.sendToTarget();
        return true;
    }
});
```

### 3. **iOS WebView (Se necessário)**

#### **WKWebView Configuration**
```swift
// No ViewController.swift
let configuration = WKWebViewConfiguration()
let userContentController = WKUserContentController()

// Adicionar handler para links externos
userContentController.add(self, name: "openExternal")
configuration.userContentController = userContentController

// Implementar o handler
func userContentController(_ userContentController: WKUserContentController, 
                          didReceive message: WKScriptMessage) {
    if message.name == "openExternal" {
        if let urlString = message.body as? String,
           let url = URL(string: urlString) {
            UIApplication.shared.open(url)
        }
    }
}
```

## 🧪 Como Testar

### **1. Verificar Detecção de WebView**
```javascript
// No console do navegador/WebView
console.log('WebView detectado:', window.navigator.userAgent.includes('wv'));
console.log('Android interface:', !!window.Android);
console.log('iOS interface:', !!window.webkit?.messageHandlers);
```

### **2. Testar Clique em Anúncio**
1. Abrir app no WebView
2. Navegar para página com anúncio
3. Clicar no anúncio
4. Verificar se abre no navegador externo

### **3. Logs de Debug**
- ✅ `📱 [AdSense] WebView detectado`
- ✅ `🔗 [AdSense] Clique em anúncio detectado`
- ✅ `📱 [AdSense] WebView detectado - clique interceptado`

## 📊 Status da Implementação

### ✅ **Implementado (Frontend)**
- [x] Detecção automática de WebView
- [x] Interceptação de cliques em anúncios
- [x] Interface JavaScript para Android
- [x] Interface JavaScript para iOS
- [x] Logs de debug

### ⏳ **Pendente (Nativo)**
- [ ] JavaScript Interface Android (`openExternalUrl`)
- [ ] WebViewClient override para URLs de anúncios
- [ ] WebChromeClient para novas janelas
- [ ] WKWebView handler iOS (se necessário)

## 🎯 Resultado Esperado
Após implementação completa:
1. **Usuário clica no anúncio** → Detectado pelo JavaScript
2. **JavaScript chama interface nativa** → `Android.openExternalUrl(url)`
3. **App Android abre navegador** → Intent para ACTION_VIEW
4. **Usuário navega no anúncio** → Fora do WebView
5. **Usuário volta ao app** → Botão voltar do Android

## 📞 Suporte
Se precisar de ajuda com a implementação Android, posso fornecer código mais específico baseado na estrutura do seu projeto.
