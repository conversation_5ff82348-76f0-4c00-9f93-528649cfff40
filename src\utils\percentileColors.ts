/**
 * Determina as cores apropriadas para percentis baseado em interpretação clínica
 * P3-P97 = Normal (verde), <P3 = Baixo (vermelho), >P97 = Alto (laranja)
 */

export interface PercentileColorScheme {
  bgColor: string;
  textColor: string;
  borderColor: string;
  status: 'normal' | 'baixo' | 'alto';
}

export const getPercentileColors = (percentile: number): PercentileColorScheme => {
  if (percentile < 0.5) {
    // Percentil extremamente baixo - vermelho escuro (crítico)
    return {
      bgColor: 'bg-red-100',
      textColor: 'text-red-800',
      borderColor: 'border-red-300',
      status: 'baixo'
    };
  } else if (percentile < 3) {
    // Percentil muito baixo - vermelho (preocupante)
    return {
      bgColor: 'bg-red-50',
      textColor: 'text-red-700',
      borderColor: 'border-red-200',
      status: 'baixo'
    };
  } else if (percentile < 10) {
    // Percentil baixo - amarelo (atenção)
    return {
      bgColor: 'bg-yellow-50',
      textColor: 'text-yellow-700',
      borderColor: 'border-yellow-200',
      status: 'baixo'
    };
  } else if (percentile > 97) {
    // Percentil muito alto - laranja (investigar)
    return {
      bgColor: 'bg-orange-50',
      textColor: 'text-orange-700',
      borderColor: 'border-orange-200',
      status: 'alto'
    };
  } else if (percentile > 90) {
    // Percentil alto - azul (monitorar)
    return {
      bgColor: 'bg-blue-50',
      textColor: 'text-blue-700',
      borderColor: 'border-blue-200',
      status: 'alto'
    };
  } else {
    // Percentil normal (P10-P90) - verde (adequado)
    return {
      bgColor: 'bg-green-50',
      textColor: 'text-green-700',
      borderColor: 'border-green-200',
      status: 'normal'
    };
  }
};

/**
 * Formata a interpretação do percentil para exibição
 */
export const formatPercentileInterpretation = (interpretation: string): string => {
  const interpretationMap: { [key: string]: string } = {
    'muito_baixo': 'Muito baixo',
    'baixo': 'Baixo',
    'normal': 'Normal',
    'alto': 'Alto',
    'muito_alto': 'Muito alto'
  };

  return interpretationMap[interpretation] || interpretation;
};

/**
 * Determina o ícone apropriado para o status do percentil
 */
export const getPercentileIcon = (status: 'normal' | 'baixo' | 'alto'): string => {
  switch (status) {
    case 'normal':
      return '✅';
    case 'baixo':
      return '⚠️';
    case 'alto':
      return '🔶';
    default:
      return '📊';
  }
};
