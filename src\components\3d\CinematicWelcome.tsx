import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { StarField } from "./StarField";
import { EnvelopeCard } from "./EnvelopeCard";
import { FeatureShowcase } from "./FeatureShowcase";
import { CounterAnimation } from "./CounterAnimation";
import { SoundManager } from "./SoundManager";
import { CinematicLoader } from "./CinematicLoader";

interface CinematicWelcomeProps {
  onComplete: () => void;
  onStartStudy: () => void;
}

type CinematicStage =
  | 'loading'
  | 'starfield'
  | 'envelope-arrival'
  | 'envelope-opening'
  | 'welcome-message'
  | 'counter-animation'
  | 'feature-showcase'
  | 'final-cta';

export const CinematicWelcome: React.FC<CinematicWelcomeProps> = ({
  onComplete,
  onStartStudy
}) => {
  const [stage, setStage] = useState<CinematicStage>('loading');
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);

  useEffect(() => {
    const sequence = async () => {
      // Aguardar loading completar (será controlado pelo CinematicLoader)
      if (stage === 'starfield') {
        await new Promise(resolve => setTimeout(resolve, 2500)); // Starfield adequado
        setStage('envelope-arrival');

        await new Promise(resolve => setTimeout(resolve, 3000)); // Envelope chega adequado
        setStage('envelope-opening');
      }
    };

    sequence();
  }, [stage]);

  const handleEnvelopeClick = () => {
    setStage('welcome-message');
  };

  const handleContinueClick = () => {
    // Mostrar showcase rápido dos recursos
    setStage('feature-showcase');
  };

  const handleCounterComplete = () => {
    setStage('feature-showcase');
  };

  const handleShowcaseComplete = () => {
    setStage('final-cta');
  };

  const handleStartStudy = () => {
    onStartStudy();
  };

  const handleSkip = () => {
    onComplete();
  };

  const handleLoadingComplete = () => {
    setStage('starfield');
  };

  return (
    <div className="fixed inset-0 z-50 bg-black overflow-hidden">
      {/* Sound Manager */}
      <SoundManager isEnabled={isAudioEnabled} currentStage={stage} />

      {/* Starfield Background - Always Present */}
      <StarField />

      {/* Audio Toggle */}
      <motion.button
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1 }}
        onClick={async () => {
          setIsAudioEnabled(!isAudioEnabled);
          // Ativar contexto de áudio com interação do usuário
          if (!isAudioEnabled) {
            try {
              const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
              if (audioContext.state === 'suspended') {
                await audioContext.resume();
              }
            } catch (error) {
              console.warn('Failed to activate audio:', error);
            }
          }
        }}
        className="absolute top-4 right-4 z-50 bg-white/10 backdrop-blur-sm rounded-full p-3 text-white hover:bg-white/20 transition-colors"
      >
        {isAudioEnabled ? '🔊' : '🔇'}
      </motion.button>

      {/* Skip Button */}
      <motion.button
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1 }}
        onClick={handleSkip}
        className="absolute top-4 left-4 z-50 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 text-white hover:bg-white/20 transition-colors text-sm"
      >
        Pular Introdução
      </motion.button>

      {/* Stage Content */}
      <AnimatePresence mode="wait">
        {stage === 'loading' && (
          <CinematicLoader
            key="loading"
            isVisible={true}
            onComplete={handleLoadingComplete}
          />
        )}

        {stage === 'starfield' && (
          <motion.div
            key="starfield"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="flex items-center justify-center h-full"
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.5, duration: 1 }}
              className="text-center text-white"
            >
              <motion.h1
                className="text-6xl font-bold mb-4 bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 text-transparent bg-clip-text"
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 1, duration: 0.8 }}
              >
                PedBook
              </motion.h1>
              <motion.p
                className="text-xl text-gray-300"
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 1.5, duration: 0.8 }}
              >
                Preparando sua experiência...
              </motion.p>
            </motion.div>
          </motion.div>
        )}

        {(stage === 'envelope-arrival' || stage === 'envelope-opening') && (
          <EnvelopeCard
            key="envelope"
            stage={stage}
            onEnvelopeClick={handleEnvelopeClick}
            isAudioEnabled={isAudioEnabled}
          />
        )}

        {stage === 'welcome-message' && (
          <motion.div
            key="welcome"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="flex items-center justify-center h-full p-8"
          >
            <div className="bg-white/95 backdrop-blur-lg rounded-3xl p-12 max-w-2xl text-center shadow-2xl border border-white/20">
              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.2 }}
              >
                <h2 className="text-4xl font-bold text-gray-800 mb-6">
                  🎉 Bem-vindo ao PedBook!
                </h2>
                <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                  Você agora tem acesso à nossa plataforma especializada em <span className="font-bold text-blue-600">pediatria</span> para residência médica!
                </p>
                <div className="space-y-4">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={handleStartStudy}
                    className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-2xl text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                  >
                    🎯 Começar Estudos Agora
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={handleContinueClick}
                    className="w-full bg-gray-100 text-gray-700 px-8 py-3 rounded-2xl text-base font-medium hover:bg-gray-200 transition-all duration-300"
                  >
                    ✨ Ver Recursos da Plataforma
                  </motion.button>
                </div>
              </motion.div>
            </div>
          </motion.div>
        )}

        {stage === 'counter-animation' && (
          <CounterAnimation
            key="counter"
            onComplete={handleCounterComplete}
            isAudioEnabled={isAudioEnabled}
          />
        )}

        {stage === 'feature-showcase' && (
          <FeatureShowcase
            key="showcase"
            onComplete={handleShowcaseComplete}
            isAudioEnabled={isAudioEnabled}
          />
        )}

        {stage === 'final-cta' && (
          <motion.div
            key="final-cta"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="flex items-center justify-center h-full p-8"
          >
            <div className="bg-white/95 backdrop-blur-lg rounded-3xl p-12 max-w-2xl text-center shadow-2xl border border-white/20">
              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.2 }}
              >
                <h2 className="text-4xl font-bold text-gray-800 mb-6">
                  🚀 Pronto para começar?
                </h2>
                <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                  Agora você tem acesso completo à nossa plataforma de estudos. Vamos começar sua jornada na pediatria!
                </p>
                <div className="space-y-4">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={handleStartStudy}
                    className="w-full bg-gradient-to-r from-green-600 to-emerald-600 text-white px-8 py-4 rounded-2xl text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                  >
                    🎯 Começar Estudos Agora
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={handleSkip}
                    className="w-full bg-gray-100 text-gray-700 px-8 py-3 rounded-2xl text-base font-medium hover:bg-gray-200 transition-all duration-300"
                  >
                    Explorar Mais Tarde
                  </motion.button>
                </div>
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
