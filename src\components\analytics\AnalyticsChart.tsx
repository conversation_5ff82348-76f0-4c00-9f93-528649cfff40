import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { lazy, Suspense } from 'react';

// Lazy load recharts para melhor performance
const LazyChart = lazy(() =>
  import('./LazyAnalyticsChart').then(module => ({
    default: module.LazyAnalyticsChart
  }))
);

interface AnalyticsChartProps {
  title: string;
  description?: string;
  data: any[];
  type: 'line' | 'bar' | 'pie' | 'area';
  dataKey: string;
  xAxisKey?: string;
  height?: number;
  color?: string;
  isHourlyData?: boolean; // Nova prop para identificar dados por hora
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

export const AnalyticsChart = ({
  title,
  description,
  data,
  type,
  dataKey,
  xAxisKey = 'name',
  height = 300,
  color = '#8884d8',
  isHourlyData = false
}: AnalyticsChartProps) => {
  const ChartSkeleton = () => (
    <div className="w-full animate-pulse" style={{ height }}>
      <div className="bg-gray-200 dark:bg-gray-700 rounded h-full flex items-center justify-center">
        <div className="text-gray-500 dark:text-gray-400 text-sm">Carregando gráfico...</div>
      </div>
    </div>
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        {data && data.length > 0 ? (
          <Suspense fallback={<ChartSkeleton />}>
            <LazyChart
              data={data}
              type={type}
              dataKey={dataKey}
              xAxisKey={xAxisKey}
              height={height}
              color={color}
              isHourlyData={isHourlyData}
            />
          </Suspense>
        ) : (
          <div className="flex items-center justify-center h-[300px] text-muted-foreground">
            Nenhum dado disponível para o período selecionado
          </div>
        )}
      </CardContent>
    </Card>
  );
};
