/**
 * Formata idade gestacional de decimal para formato legível
 * Converte valores como 39.714285714285715 para "39 semanas e 5 dias"
 */
export const formatGestationalAge = (gestationalAgeWeeks: number): string => {
  if (!gestationalAgeWeeks || gestationalAgeWeeks <= 0) {
    return "Não informado";
  }

  const weeks = Math.floor(gestationalAgeWeeks);
  const days = Math.round((gestationalAgeWeeks - weeks) * 7);

  // Ajustar se os dias calculados forem 7 (adicionar uma semana)
  if (days === 7) {
    return `${weeks + 1} semanas`;
  }

  if (weeks === 0) {
    return `${days} ${days === 1 ? 'dia' : 'dias'}`;
  }

  if (days === 0) {
    return `${weeks} ${weeks === 1 ? 'semana' : 'semanas'}`;
  }

  return `${weeks} ${weeks === 1 ? 'semana' : 'semanas'} e ${days} ${days === 1 ? 'dia' : 'dias'}`;
};

/**
 * Formata idade gestacional para exibição compacta
 * Exemplo: "39s5d" ou "32s0d"
 */
export const formatGestationalAgeCompact = (gestationalAgeWeeks: number): string => {
  if (!gestationalAgeWeeks || gestationalAgeWeeks <= 0) {
    return "0s0d";
  }

  const weeks = Math.floor(gestationalAgeWeeks);
  const days = Math.round((gestationalAgeWeeks - weeks) * 7);

  // Ajustar se os dias calculados forem 7
  if (days === 7) {
    return `${weeks + 1}s0d`;
  }

  return `${weeks}s${days}d`;
};

/**
 * Converte idade gestacional de semanas+dias para decimal
 * Exemplo: 39 semanas e 5 dias = 39.714285714285715
 */
export const gestationalAgeToDecimal = (weeks: number, days: number): number => {
  return weeks + (days / 7);
};

/**
 * Converte idade gestacional decimal para semanas+dias
 * Exemplo: 39.714285714285715 = { weeks: 39, days: 5 }
 */
export const decimalToGestationalAge = (gestationalAgeWeeks: number): { weeks: number; days: number } => {
  if (!gestationalAgeWeeks || gestationalAgeWeeks <= 0) {
    return { weeks: 0, days: 0 };
  }

  const weeks = Math.floor(gestationalAgeWeeks);
  let days = Math.round((gestationalAgeWeeks - weeks) * 7);

  // Ajustar se os dias calculados forem 7
  if (days === 7) {
    return { weeks: weeks + 1, days: 0 };
  }

  return { weeks, days };
};
