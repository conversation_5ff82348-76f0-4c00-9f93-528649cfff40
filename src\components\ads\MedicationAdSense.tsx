import React, { useEffect, useRef } from 'react';

interface MedicationAdSenseProps {
  className?: string;
}

declare global {
  interface Window {
    adsbygoogle: any[];
    Android?: {
      onBackPressed?: () => boolean;
      setBackButtonHandler?: (handler: () => boolean) => void;
      openExternalUrl?: (url: string) => void;
    };
    webkit?: {
      messageHandlers?: {
        openExternal?: {
          postMessage: (message: string) => void;
        };
      };
    };
  }
}

export const MedicationAdSense: React.FC<MedicationAdSenseProps> = ({ className = "" }) => {
  const adRef = useRef<HTMLDivElement>(null);
  const hasAdLoaded = useRef(false);

  useEffect(() => {
    // Só carregar o anúncio uma vez
    if (hasAdLoaded.current) return;

    const loadAd = () => {
      try {
        // Verificar se o AdSense está disponível
        if (typeof window !== 'undefined' && window.adsbygoogle) {
          console.log('📢 [AdSense] Carregando anúncio de medicamento...');
          window.adsbygoogle.push({});
          hasAdLoaded.current = true;
        }
      } catch (error) {
        console.warn('⚠️ [AdSense] Erro ao carregar anúncio:', error);
      }
    };

    // Carregar imediatamente se AdSense já estiver disponível
    if (window.adsbygoogle) {
      loadAd();
    } else {
      // Aguardar AdSense carregar
      const checkAdSense = setInterval(() => {
        if (window.adsbygoogle) {
          loadAd();
          clearInterval(checkAdSense);
        }
      }, 100);

      // Timeout de 10 segundos
      setTimeout(() => {
        clearInterval(checkAdSense);
      }, 10000);
    }

    // Interceptar cliques em anúncios para WebView
    const handleAdClick = (event: Event) => {
      const target = event.target as HTMLElement;

      // Verificar se o clique foi em um anúncio ou link dentro do anúncio
      if (target.closest('.adsbygoogle') || target.closest('iframe[src*="googlesyndication"]')) {
        console.log('🔗 [AdSense] Clique em anúncio detectado - tentando abrir externamente');

        // Tentar diferentes métodos para abrir externamente
        const tryOpenExternal = (url?: string) => {
          try {
            // Método 1: window.open com _blank
            if (url) {
              window.open(url, '_blank', 'noopener,noreferrer');
              return;
            }

            // Método 2: Para WebView Android
            if (window.Android && window.Android.openExternalUrl) {
              window.Android.openExternalUrl(url || '');
              return;
            }

            // Método 3: Para WebView iOS
            if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.openExternal) {
              window.webkit.messageHandlers.openExternal.postMessage(url || '');
              return;
            }

            console.log('📱 [AdSense] WebView detectado - clique interceptado');
          } catch (error) {
            console.warn('⚠️ [AdSense] Erro ao abrir link externo:', error);
          }
        };

        // Tentar extrair URL do anúncio
        const adFrame = target.closest('iframe');
        if (adFrame && adFrame.src) {
          tryOpenExternal(adFrame.src);
        } else {
          tryOpenExternal();
        }
      }
    };

    // Adicionar listener para cliques
    document.addEventListener('click', handleAdClick, true);

    return () => {
      document.removeEventListener('click', handleAdClick, true);
    };
  }, []);

  return (
    <div 
      ref={adRef}
      className={`w-full flex justify-center my-6 ${className}`}
      style={{ minHeight: '90px' }} // Altura mínima para evitar layout shift
    >
      {/* Anúncio AdSense Responsivo */}
      <ins 
        className="adsbygoogle"
        style={{
          display: 'inline-block',
          width: '100%',
          maxWidth: '500px',
          height: '90px'
        }}
        data-ad-client="ca-pub-4018898302361000"
        data-ad-slot="4518452591"
        data-ad-format="auto"
        data-full-width-responsive="true"
      />
    </div>
  );
};

export default MedicationAdSense;
