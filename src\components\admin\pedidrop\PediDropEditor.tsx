import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Save,
  X,
  Plus,
  Trash2,
  GripVertical,
  Clock,
  Calendar,
  BookOpen,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';

import { MarkdownEditor } from '@/components/ui/MarkdownEditor';
import { useCreatePediDrop, useUpdatePediDrop, type PediDropPost } from '@/hooks/usePediDrop';

interface Section {
  id: string;
  title: string;
  content: string;
}

interface PediDropEditorProps {
  drop?: PediDropPost;
  onClose: () => void;
}

export const PediDropEditor: React.FC<PediDropEditorProps> = ({ drop, onClose }) => {
  // Função para obter data/hora atual em São Paulo (para input datetime-local)
  const getSaoPauloDateTime = () => {
    const now = new Date();
    // Para datetime-local, precisamos da data local sem conversão de timezone
    const year = now.getFullYear();
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');
    const hour = now.getHours().toString().padStart(2, '0');
    const minute = now.getMinutes().toString().padStart(2, '0');

    return `${year}-${month}-${day}T${hour}:${minute}`;
  };

  const [formData, setFormData] = useState({
    title: '',
    summary: '',
    main_topic: '',
    reading_time: 5,
    pub_date: getSaoPauloDateTime(),
    is_published: false,
  });

  const [sections, setSections] = useState<Section[]>([]);
  const [references, setReferences] = useState<Array<{title: string; url: string}>>([{title: '', url: ''}]);
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());

  // Hooks de mutação
  const createMutation = useCreatePediDrop();
  const updateMutation = useUpdatePediDrop();

  // Carregar dados se estiver editando
  useEffect(() => {
    if (drop) {
      // Função para converter data do banco para input datetime-local
      const formatDateForInput = (dateString: string) => {
        const date = new Date(dateString);
        // Ajustar para timezone local para exibir corretamente no input
        const timezoneOffset = date.getTimezoneOffset() * 60000;
        const localDate = new Date(date.getTime() - timezoneOffset);
        return localDate.toISOString().slice(0, 16);
      };

      setFormData({
        title: drop.title || '',
        summary: drop.summary || '',
        main_topic: drop.main_topic || '',
        reading_time: drop.reading_time || 5,
        pub_date: drop.pub_date ? formatDateForInput(drop.pub_date) : getSaoPauloDateTime(),
        is_published: drop.is_published || false,
      });

      // Carregar seções
      if (drop.sections && drop.sections.length > 0) {
        setSections(drop.sections.map((section, index) => ({
          id: `section-${index}`,
          title: section.title || '',
          content: section.content || ''
        })));
      }

      // Carregar referências
      if (drop.references && drop.references.length > 0) {
        setReferences(drop.references.map(ref => ({
          title: ref.title || ref.reference_text || '',
          url: ref.url || ''
        })));
      } else {
        setReferences([{title: '', url: ''}]);
      }
    }
  }, [drop]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addSection = () => {
    const newSection: Section = {
      id: `section-${Date.now()}`,
      title: '',
      content: ''
    };
    setSections(prev => [...prev, newSection]);
    setExpandedSections(prev => new Set([...prev, newSection.id]));
  };

  const updateSection = (id: string, field: 'title' | 'content', value: string) => {
    setSections(prev => prev.map(section => 
      section.id === id ? { ...section, [field]: value } : section
    ));
  };

  const removeSection = (id: string) => {
    setSections(prev => prev.filter(section => section.id !== id));
    setExpandedSections(prev => {
      const newSet = new Set(prev);
      newSet.delete(id);
      return newSet;
    });
  };

  const toggleSection = (id: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  const addReference = () => {
    setReferences(prev => [...prev, {title: '', url: ''}]);
  };

  const updateReference = (index: number, field: 'title' | 'url', value: string) => {
    setReferences(prev => prev.map((ref, i) =>
      i === index ? { ...ref, [field]: value } : ref
    ));
  };

  const removeReference = (index: number) => {
    setReferences(prev => prev.filter((_, i) => i !== index));
  };

  const handleSave = async () => {
    try {
      // Salvar a data como está (assumindo que já está em horário local)
      let pubDateISO: string;

      if (formData.pub_date) {
        // Criar data local sem conversão de timezone
        const localDate = new Date(formData.pub_date);
        pubDateISO = localDate.toISOString();
      } else {
        pubDateISO = new Date().toISOString();
      }

      console.log('💾 Salvando PediDrop:');
      console.log('  - Data do input:', formData.pub_date);
      console.log('  - Data final ISO:', pubDateISO);

      // Preparar dados para salvar
      const dataToSave = {
        title: formData.title,
        summary: formData.summary,
        main_topic: formData.main_topic,
        reading_time: formData.reading_time,
        pub_date: pubDateISO,
        is_published: formData.is_published,
        sections: sections.map(section => ({
          title: section.title,
          content: section.content
        })),
        references: references.filter(ref => ref.title.trim() !== '' || ref.url.trim() !== '')
      };

      if (drop?.id) {
        // Atualizar
        await updateMutation.mutateAsync({
          ...dataToSave,
          id: drop.id
        });
      } else {
        // Criar novo
        await createMutation.mutateAsync(dataToSave);
      }

      onClose();
    } catch (error) {
      console.error('Erro ao salvar PediDrop:', error);
      alert('Erro ao salvar PediDrop. Tente novamente.');
    }
  };

  const isLoading = createMutation.isPending || updateMutation.isPending;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-slate-900">
      <div className="max-w-6xl mx-auto px-4 py-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between mb-6"
        >
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {drop ? 'Editar PediDrop' : 'Novo PediDrop'}
            </h1>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Crie atualizações clínicas estruturadas e envolventes
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" onClick={onClose}>
              <X className="h-4 w-4 mr-2" />
              Cancelar
            </Button>
            <Button 
              onClick={handleSave} 
              disabled={isLoading || !formData.title || !formData.summary}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Save className="h-4 w-4 mr-2" />
              {isLoading ? 'Salvando...' : 'Salvar'}
            </Button>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Formulário Principal */}
          <div className="lg:col-span-3 space-y-4">
            {/* Informações Básicas */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Calendar className="h-4 w-4" />
                  Informações Básicas
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <Label htmlFor="title">Título *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="Ex: 🍯 VYKAT XR: primeira arma contra a hiperfagia na Prader-Willi"
                    className="mt-1"
                  />
                </div>

                <div>
                  <MarkdownEditor
                    label="Resumo/Introdução *"
                    value={formData.summary}
                    onChange={(value) => handleInputChange('summary', value)}
                    placeholder="👋 Bom dia, Droppers! Hoje no PediDrop: ..."
                    height={120}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="reading_time">Tempo de Leitura (min)</Label>
                    <Input
                      id="reading_time"
                      type="number"
                      value={formData.reading_time}
                      onChange={(e) => handleInputChange('reading_time', parseInt(e.target.value) || 5)}
                      min="1"
                      max="15"
                      className="mt-1"
                    />
                  </div>

                  <div>
                    <Label htmlFor="pub_date">Data de Publicação</Label>
                    <Input
                      id="pub_date"
                      type="datetime-local"
                      value={formData.pub_date}
                      onChange={(e) => handleInputChange('pub_date', e.target.value)}
                      className="mt-1"
                    />
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="is_published"
                    checked={formData.is_published}
                    onChange={(e) => handleInputChange('is_published', e.target.checked)}
                    className="rounded border-gray-300"
                  />
                  <Label htmlFor="is_published" className="text-sm font-medium">
                    Publicar imediatamente
                  </Label>
                </div>

                <div>
                  <Label htmlFor="main_topic">Tópico Principal</Label>
                  <Input
                    id="main_topic"
                    value={formData.main_topic}
                    onChange={(e) => handleInputChange('main_topic', e.target.value)}
                    placeholder="Ex: VYKAT XR (diazoxide choline) - Primeira terapia para hiperfagia em Síndrome de Prader-Willi"
                    className="mt-1"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Seções de Conteúdo */}
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <BookOpen className="h-4 w-4" />
                    Seções de Conteúdo
                  </CardTitle>
                  <Button onClick={addSection} size="sm">
                    <Plus className="h-4 w-4 mr-1" />
                    Adicionar
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {sections.length === 0 ? (
                  <div className="text-center py-6 text-gray-500 dark:text-gray-400">
                    <BookOpen className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">Nenhuma seção criada ainda.</p>
                    <p className="text-xs">Clique em "Adicionar" para começar.</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {sections.map((section, index) => (
                      <div key={section.id} className="border border-gray-200 dark:border-gray-700 rounded-lg">
                        <div
                          className="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-800"
                          onClick={() => toggleSection(section.id)}
                        >
                          <div className="flex items-center gap-3">
                            <GripVertical className="h-4 w-4 text-gray-400" />
                            <Badge variant="outline">Seção {index + 1}</Badge>
                            <span className="font-medium text-gray-900 dark:text-gray-100">
                              {section.title || 'Título da seção'}
                            </span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                removeSection(section.id);
                              }}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                            {expandedSections.has(section.id) ? (
                              <ChevronUp className="h-4 w-4" />
                            ) : (
                              <ChevronDown className="h-4 w-4" />
                            )}
                          </div>
                        </div>
                        
                        {expandedSections.has(section.id) && (
                          <div className="p-3 border-t border-gray-200 dark:border-gray-700 space-y-3">
                            <div>
                              <Label className="text-sm">Título da Seção</Label>
                              <Input
                                value={section.title}
                                onChange={(e) => updateSection(section.id, 'title', e.target.value)}
                                placeholder="Ex: Aplicação Prática, Pérolas Clínicas..."
                                className="mt-1"
                              />
                            </div>
                            <div>
                              <MarkdownEditor
                                label="Conteúdo"
                                value={section.content}
                                onChange={(value) => updateSection(section.id, 'content', value)}
                                placeholder="Escreva o conteúdo desta seção..."
                                height={150}
                              />
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-4">
            {/* Referências */}
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <BookOpen className="h-4 w-4" />
                    Referências
                  </CardTitle>
                  <Button onClick={addReference} size="sm" variant="outline">
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                {references.map((reference, index) => (
                  <div key={index} className="space-y-2 p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <div className="flex items-center justify-between">
                      <Label className="text-xs font-medium">Referência {index + 1}</Label>
                      {references.length > 1 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeReference(index)}
                          className="text-red-600 hover:text-red-700 h-6 w-6 p-0"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      )}
                    </div>

                    <div>
                      <Label htmlFor={`ref-title-${index}`} className="text-xs text-gray-600 dark:text-gray-400">
                        Título
                      </Label>
                      <Input
                        id={`ref-title-${index}`}
                        value={reference.title}
                        onChange={(e) => updateReference(index, 'title', e.target.value)}
                        placeholder="Ex: FDA Approves VYKAT XR..."
                        className="mt-1 text-sm"
                      />
                    </div>

                    <div>
                      <Label htmlFor={`ref-url-${index}`} className="text-xs text-gray-600 dark:text-gray-400">
                        URL
                      </Label>
                      <Input
                        id={`ref-url-${index}`}
                        value={reference.url}
                        onChange={(e) => updateReference(index, 'url', e.target.value)}
                        placeholder="https://www.fda.gov/..."
                        className="mt-1 text-sm"
                      />
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Preview Info */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Clock className="h-4 w-4" />
                  Informações
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-xs">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Seções:</span>
                  <span className="font-medium">{sections.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Referências:</span>
                  <span className="font-medium">{references.filter(ref => ref.title.trim() || ref.url.trim()).length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Tempo:</span>
                  <span className="font-medium">{formData.reading_time} min</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Status:</span>
                  <span className={`font-medium ${formData.is_published ? 'text-green-600' : 'text-yellow-600'}`}>
                    {formData.is_published ? 'Publicado' : 'Rascunho'}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};
