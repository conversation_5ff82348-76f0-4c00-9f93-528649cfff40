/**
 * Formata idade em meses para formato legível sem arredondamento
 * Mantém a precisão exata da idade para melhor compreensão clínica
 * Usa 30.44 dias por mês para maior precisão (mesmo padrão do PatientSummaryHeader)
 */
export const formatAge = (ageInMonths: number): string => {
  // Para idades muito pequenas (< 1 mês), mostrar em dias
  if (ageInMonths < 1) {
    const days = Math.round(ageInMonths * 30.44);
    return `${days} ${days === 1 ? 'dia' : 'dias'}`;
  }

  // Calcular anos e meses sem arredondamento
  const totalMonths = ageInMonths;
  const years = Math.floor(totalMonths / 12);
  const remainingMonths = totalMonths - (years * 12);

  // Calcular meses e dias restantes
  const months = Math.floor(remainingMonths);
  const fractionalMonth = remainingMonths - months;
  const days = Math.round(fractionalMonth * 30.44); // Usando 30.44 dias por mês

  // Formatação baseada na idade
  if (years === 0) {
    // Menos de 1 ano
    if (days === 0) {
      return `${months} ${months === 1 ? 'mês' : 'meses'}`;
    } else {
      return `${months} ${months === 1 ? 'mês' : 'meses'} e ${days} ${days === 1 ? 'dia' : 'dias'}`;
    }
  } else {
    // 1 ano ou mais
    if (months === 0 && days === 0) {
      return `${years} ${years === 1 ? 'ano' : 'anos'}`;
    } else if (days === 0) {
      return `${years} ${years === 1 ? 'ano' : 'anos'} e ${months} ${months === 1 ? 'mês' : 'meses'}`;
    } else if (months === 0) {
      return `${years} ${years === 1 ? 'ano' : 'anos'} e ${days} ${days === 1 ? 'dia' : 'dias'}`;
    } else {
      return `${years} ${years === 1 ? 'ano' : 'anos'}, ${months} ${months === 1 ? 'mês' : 'meses'} e ${days} ${days === 1 ? 'dia' : 'dias'}`;
    }
  }
};

/**
 * Versão simplificada para casos onde precisamos de formato mais compacto
 * Usa 30.44 dias por mês para consistência
 */
export const formatAgeCompact = (ageInMonths: number): string => {
  if (ageInMonths < 1) {
    const days = Math.round(ageInMonths * 30.44);
    return `${days}d`;
  }

  const years = Math.floor(ageInMonths / 12);
  const months = Math.floor(ageInMonths % 12);
  const days = Math.round((ageInMonths % 1) * 30.44); // Usando 30.44 dias por mês

  if (years === 0) {
    if (days === 0) {
      return `${months}m`;
    } else {
      return `${months}m${days}d`;
    }
  } else {
    if (months === 0 && days === 0) {
      return `${years}a`;
    } else if (days === 0) {
      return `${years}a${months}m`;
    } else {
      return `${years}a${months}m${days}d`;
    }
  }
};

/**
 * Formata idade para exibição médica (mais detalhada)
 */
export const formatAgeMedical = (ageInMonths: number, useCorrectedAge: boolean = false): string => {
  const formattedAge = formatAge(ageInMonths);
  const ageType = useCorrectedAge ? 'idade corrigida' : 'idade cronológica';
  
  return `${formattedAge} (${ageType})`;
};
