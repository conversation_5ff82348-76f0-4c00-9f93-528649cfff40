import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Calculator, FileText, ExternalLink } from "lucide-react";

interface MedicationData {
  id: string;
  name: string;
  slug: string;
  brands?: string;
  description?: string;
  has_bula: boolean;
  has_calculation: boolean;
}

interface MedicationResourcesDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  medications: MedicationData[];
  title: string;
}

export const MedicationResourcesDialog: React.FC<MedicationResourcesDialogProps> = ({
  open,
  onOpenChange,
  medications,
  title,
}) => {
  const handleLinkClick = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <span className="text-2xl">💊</span>
            {title}
          </DialogTitle>
          <DialogDescription>
            Recursos disponíveis na plataforma PedBook para os medicamentos mencionados.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 mt-4">
          {medications.map((medication) => (
            <div
              key={medication.id}
              className="border rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            >
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h3 className="font-semibold text-lg">{medication.name}</h3>
                  {medication.brands && (
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Marcas: {medication.brands}
                    </p>
                  )}
                  {medication.description && (
                    <p className="text-sm text-gray-700 dark:text-gray-300 mt-1">
                      {medication.description}
                    </p>
                  )}
                </div>
              </div>

              <div className="flex flex-wrap gap-2">
                {medication.has_calculation && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleLinkClick(`https://pedb.com.br/medicamentos/${medication.slug}`)}
                    className="flex items-center gap-2"
                  >
                    <Calculator className="w-4 h-4" />
                    Calcular dosagem
                    <ExternalLink className="w-3 h-3" />
                  </Button>
                )}

                {medication.has_bula && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleLinkClick(`https://pedb.com.br/bulas-profissionais/${medication.slug}`)}
                    className="flex items-center gap-2"
                  >
                    <FileText className="w-4 h-4" />
                    Bula profissional
                    <ExternalLink className="w-3 h-3" />
                  </Button>
                )}

                {!medication.has_calculation && !medication.has_bula && (
                  <Badge variant="secondary" className="text-xs">
                    Recursos em desenvolvimento
                  </Badge>
                )}
              </div>
            </div>
          ))}
        </div>

        <div className="mt-6 pt-4 border-t">
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              💡 Recursos internos da plataforma PedBook
            </p>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onOpenChange(false)}
            >
              Fechar
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
