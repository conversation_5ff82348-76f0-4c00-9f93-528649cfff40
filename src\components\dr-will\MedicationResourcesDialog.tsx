import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Calculator, FileText, ExternalLink } from "lucide-react";

interface MedicationData {
  id: string;
  name: string;
  slug: string;
  brands?: string;
  description?: string;
  has_bula: boolean;
  has_calculation: boolean;
}

interface MedicationResourcesDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  medications: MedicationData[];
  title: string;
}

export const MedicationResourcesDialog: React.FC<MedicationResourcesDialogProps> = ({
  open,
  onOpenChange,
  medications,
  title,
}) => {
  const handleLinkClick = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[95vw] max-w-4xl h-[90dvh] max-h-[90dvh] flex flex-col p-0 gap-0">
        <DialogHeader className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
          <DialogTitle className="flex items-center gap-3 text-xl">
            <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center text-white text-lg shadow-lg">
              💊
            </div>
            <div>
              <div className="font-bold text-gray-900 dark:text-white">{title}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400 font-normal">
                Recursos da plataforma PedBook
              </div>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto px-6 py-4">
          <div className="space-y-4">
            {medications.map((medication) => (
              <div
                key={medication.id}
                className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-5 hover:shadow-md transition-all duration-200 hover:border-blue-200 dark:hover:border-blue-700"
              >
                <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
                  <div className="flex-1">
                    <h3 className="font-bold text-lg text-gray-900 dark:text-white mb-2">
                      {medication.name}
                    </h3>

                    {medication.brands && (
                      <div className="mb-3">
                        <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                          Marcas comerciais
                        </span>
                        <p className="text-sm text-gray-700 dark:text-gray-300 mt-1">
                          {medication.brands}
                        </p>
                      </div>
                    )}

                    {medication.description && (
                      <div className="mb-3">
                        <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                          Descrição
                        </span>
                        <p className="text-sm text-gray-700 dark:text-gray-300 mt-1">
                          {medication.description}
                        </p>
                      </div>
                    )}
                  </div>

                  <div className="flex flex-col sm:flex-row gap-2 sm:flex-shrink-0">
                    {medication.has_calculation && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleLinkClick(`https://pedb.com.br/medicamentos/${medication.slug}`)}
                        className="flex items-center gap-2 bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700 hover:bg-blue-100 dark:hover:bg-blue-900/30 text-blue-700 dark:text-blue-300"
                      >
                        <Calculator className="w-4 h-4" />
                        Calcular dosagem
                        <ExternalLink className="w-3 h-3" />
                      </Button>
                    )}

                    {medication.has_bula && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleLinkClick(`https://pedb.com.br/bulas-profissionais/${medication.slug}`)}
                        className="flex items-center gap-2 bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700 hover:bg-green-100 dark:hover:bg-green-900/30 text-green-700 dark:text-green-300"
                      >
                        <FileText className="w-4 h-4" />
                        Bula profissional
                        <ExternalLink className="w-3 h-3" />
                      </Button>
                    )}

                    {!medication.has_calculation && !medication.has_bula && (
                      <Badge variant="secondary" className="text-xs">
                        Recursos em desenvolvimento
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex-shrink-0 bg-gray-50 dark:bg-gray-800/50">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <p className="text-sm text-gray-600 dark:text-gray-400 font-medium">
                Recursos internos da plataforma PedBook
              </p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onOpenChange(false)}
              className="hover:bg-gray-200 dark:hover:bg-gray-700"
            >
              Fechar
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
