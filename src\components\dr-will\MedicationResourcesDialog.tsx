import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Calculator, FileText, ExternalLink } from "lucide-react";

interface MedicationData {
  id: string;
  name: string;
  slug: string;
  brands?: string;
  description?: string;
  has_bula: boolean;
  has_calculation: boolean;
}

interface MedicationResourcesDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  medications: MedicationData[];
  title: string;
}

export const MedicationResourcesDialog: React.FC<MedicationResourcesDialogProps> = ({
  open,
  onOpenChange,
  medications,
  title,
}) => {
  const handleLinkClick = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[95vw] max-w-4xl h-[90dvh] max-h-[90dvh] flex flex-col p-0 gap-0">
        <DialogHeader className="px-6 py-5 border-b border-gray-200 dark:border-gray-700 flex-shrink-0 bg-gradient-to-r from-blue-50 to-green-50 dark:from-blue-900/20 dark:to-green-900/20">
          <DialogTitle className="flex items-center gap-4 text-xl">
            <div className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 via-green-500 to-blue-600 flex items-center justify-center text-white text-xl shadow-lg">
              🔗
            </div>
            <div>
              <div className="font-bold text-gray-900 dark:text-white text-lg">{title}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400 font-normal flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                Recursos da plataforma PedBook
              </div>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto px-6 py-4">
          <div className="space-y-3">
            {medications.map((medication, index) => (
              <div
                key={medication.id}
                className="bg-gradient-to-r from-white to-gray-50 dark:from-gray-800 dark:to-gray-800/80 border border-gray-200 dark:border-gray-700 rounded-xl p-5 hover:shadow-lg transition-all duration-300 hover:border-blue-300 dark:hover:border-blue-600 group"
              >
                <div className="flex items-start gap-4">
                  {/* Número do medicamento */}
                  <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-green-500 flex items-center justify-center text-white text-sm font-bold flex-shrink-0">
                    {index + 1}
                  </div>

                  {/* Informações do medicamento */}
                  <div className="flex-1 min-w-0">
                    <h3 className="font-bold text-lg text-gray-900 dark:text-white mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                      {medication.name}
                    </h3>

                    {medication.brands && (
                      <div className="mb-3">
                        <span className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                          Marcas comerciais
                        </span>
                        <p className="text-sm text-gray-700 dark:text-gray-300 mt-1 line-clamp-2">
                          {medication.brands}
                        </p>
                      </div>
                    )}
                  </div>

                  {/* Botões de ação */}
                  <div className="flex flex-col gap-2 flex-shrink-0">
                    {medication.has_calculation && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleLinkClick(`https://pedb.com.br/medicamentos/${medication.slug}`)}
                        className="flex items-center gap-2 bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700 hover:bg-blue-100 dark:hover:bg-blue-900/30 text-blue-700 dark:text-blue-300 hover:scale-105 transition-all duration-200"
                      >
                        <Calculator className="w-4 h-4" />
                        <span className="hidden sm:inline">Calcular</span>
                        <ExternalLink className="w-3 h-3" />
                      </Button>
                    )}

                    {medication.has_bula && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleLinkClick(`https://pedb.com.br/bulas-profissionais/${medication.slug}`)}
                        className="flex items-center gap-2 bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700 hover:bg-green-100 dark:hover:bg-green-900/30 text-green-700 dark:text-green-300 hover:scale-105 transition-all duration-200"
                      >
                        <FileText className="w-4 h-4" />
                        <span className="hidden sm:inline">Bula</span>
                        <ExternalLink className="w-3 h-3" />
                      </Button>
                    )}

                    {!medication.has_calculation && !medication.has_bula && (
                      <Badge variant="secondary" className="text-xs">
                        Em desenvolvimento
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex-shrink-0 bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800/50 dark:to-blue-900/20">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" style={{animationDelay: '0.5s'}}></div>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 font-medium">
                Recursos internos da plataforma PedBook
              </p>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {medications.length} {medications.length === 1 ? 'medicamento' : 'medicamentos'}
              </span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onOpenChange(false)}
                className="hover:bg-white/50 dark:hover:bg-gray-700/50 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
              >
                Fechar
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
