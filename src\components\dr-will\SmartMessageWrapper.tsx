import React, { useEffect, useRef, useState } from 'react';
import { Message } from '@/types/chat';
import { MessageBubble } from './MessageBubble';

interface SmartMessageWrapperProps {
  message: Message;
  isLastMessage: boolean;
  onSuggestionClick?: (action: string) => void;
  onResourcesClick?: () => void;
}

export const SmartMessageWrapper: React.FC<SmartMessageWrapperProps> = ({
  message,
  isLastMessage,
  onSuggestionClick,
  onResourcesClick
}) => {
  const messageRef = useRef<HTMLDivElement>(null);
  const headerRef = useRef<HTMLDivElement>(null);
  const [previousHeight, setPreviousHeight] = useState(0);
  const [isGrowing, setIsGrowing] = useState(false);
  const scrollTimeout = useRef<NodeJS.Timeout>();

  // Detectar crescimento da mensagem (para streaming/typing effect)
  useEffect(() => {
    if (!isLastMessage || message.role !== 'assistant') return;

    const element = messageRef.current;
    if (!element) return;

    const currentHeight = element.scrollHeight;
    
    if (currentHeight > previousHeight && previousHeight > 0) {
      setIsGrowing(true);
      
      // Scroll suave para manter o início da mensagem visível
      const scrollToStart = () => {
        try {
          // Encontrar o viewport correto do ScrollArea (Radix UI)
          const viewport = element.closest('[data-radix-scroll-area-viewport]') as HTMLElement;

          if (viewport) {
            const elementRect = element.getBoundingClientRect();
            const viewportRect = viewport.getBoundingClientRect();
            const currentScrollTop = viewport.scrollTop;

            // Calcular se a mensagem está saindo da tela
            const elementTop = elementRect.top - viewportRect.top + currentScrollTop;
            const elementBottom = elementTop + currentHeight;
            const viewportBottom = currentScrollTop + viewport.clientHeight;

            // Se a mensagem está crescendo e saindo da tela, ajustar scroll
            if (elementBottom > viewportBottom) {
              // Manter o início da mensagem visível com um pequeno offset
              const targetScroll = elementTop - 20;

              viewport.scrollTo({
                top: Math.max(0, targetScroll),
                behavior: 'smooth'
              });


            }
          }
        } catch (error) {
          console.warn('⚠️ [SmartWrapper] Erro no scroll progressivo:', error);
        }
      };

      // Debounce do scroll para evitar muitas chamadas
      if (scrollTimeout.current) {
        clearTimeout(scrollTimeout.current);
      }
      
      scrollTimeout.current = setTimeout(() => {
        scrollToStart();
        setIsGrowing(false);
      }, 100);
    }

    setPreviousHeight(currentHeight);

    return () => {
      if (scrollTimeout.current) {
        clearTimeout(scrollTimeout.current);
      }
    };
  }, [message.content, isLastMessage, previousHeight]);

  // Scroll inicial quando a mensagem aparece - FOCA NO HEADER (nome/avatar) PARA AMBOS
  useEffect(() => {
    if (isLastMessage && headerRef.current) {
      const scrollToHeader = () => {
        try {
          // Encontrar o viewport correto do ScrollArea (Radix UI)
          const viewport = headerRef.current?.closest('[data-radix-scroll-area-viewport]') as HTMLElement;

          if (viewport && headerRef.current) {
            const headerRect = headerRef.current.getBoundingClientRect();
            const viewportRect = viewport.getBoundingClientRect();
            const currentScrollTop = viewport.scrollTop;

            // Posição do header relativa ao container scrollável
            const headerTop = headerRect.top - viewportRect.top + currentScrollTop;
            const offset = 10; // Pequeno offset do topo



            viewport.scrollTo({
              top: Math.max(0, headerTop - offset),
              behavior: message.role === 'user' ? 'auto' : 'smooth' // Usuário: imediato, Assistente: suave
            });
          } else {
            // Fallback: scrollIntoView no header

            headerRef.current?.scrollIntoView({
              behavior: message.role === 'user' ? 'auto' : 'smooth',
              block: 'start',
              inline: 'nearest'
            });
          }
        } catch (error) {
          console.warn('⚠️ [SmartWrapper] Erro no scroll inicial:', error);
        }
      };

      // Timing baseado no tipo de mensagem
      const delay = message.role === 'user' ? 50 : 300; // Usuário: rápido, Assistente: aguarda renderização
      setTimeout(scrollToHeader, delay);
    }
  }, [isLastMessage, message.role]);

  return (
    <div
      ref={messageRef}
      className={`scroll-target transition-all duration-200 ${isGrowing ? 'growing' : ''}`}
      data-message-role={message.role}
      data-is-last={isLastMessage}
      style={{
        scrollMarginTop: '20px', // Espaço do topo quando faz scroll
      }}
    >
      <MessageBubble
        message={message}
        headerRef={headerRef}
        onSuggestionClick={onSuggestionClick}
        onResourcesClick={onResourcesClick}
      />
    </div>
  );
};
