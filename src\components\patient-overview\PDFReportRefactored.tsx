import React from 'react';
import jsPDF from 'jspdf';
import { But<PERSON> } from "@/components/ui/button";
import { Download, FileText } from "lucide-react";
import { PDFUtils } from './pdf/PDFUtils';
import { PDFRenderer } from './pdf/PDFRenderer';
import { DataGenerators, PatientData } from './pdf/DataGenerators';

interface PDFReportRefactoredProps {
  patientData: any;
  analysisData?: any;
  supplementationData?: any;
  vaccineData?: any;
  useCorrectedAge: boolean;
}

export function PDFReportRefactored({ 
  patientData, 
  analysisData, 
  supplementationData, 
  vaccineData,
  useCorrectedAge 
}: PDFReportRefactoredProps) {

  const generatePDF = async () => {
    try {

      // 1. Preparar dados
      const processedPatientData: PatientData = {
        name: patientData.name,
        age: patientData.age,
        gender: patientData.gender,
        weight: patientData.weight,
        height: patientData.height,
        headCircumference: patientData.headCircumference,
        birthWeight: patientData.birthWeight,
        gestationalAge: patientData.gestationalAge,
        maturity: patientData.maturity,
        exclusiveBreastfeeding: patientData.exclusiveBreastfeeding,
        riskFactors: patientData.riskFactors
      };

      // 2. Gerar dados de análise se não fornecidos
      const calculatedAnalysisData = analysisData || await DataGenerators.generateAnalysisData(processedPatientData);
      const calculatedSupplementationData = supplementationData || DataGenerators.generateSupplementationData(processedPatientData);
      const calculatedVaccineData = vaccineData || DataGenerators.generateVaccineData(processedPatientData);

      // 3. Carregar logo
      const logoBase64 = await PDFUtils.loadLogo();

      // 4. Criar PDF
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4',
        compress: true,
        putOnlyUsedFonts: true,
        floatPrecision: 16
      });

      // Configurar fonte padrão
      pdf.setFont('helvetica');

      // 5. Criar renderer
      const renderer = new PDFRenderer(pdf, logoBase64);

      // 6. Renderizar seções
      renderer.renderHeader();
      renderer.renderPatientData(processedPatientData, useCorrectedAge);
      renderer.renderPerinatalData(processedPatientData, calculatedAnalysisData);
      renderer.renderAnalysis(calculatedAnalysisData, processedPatientData);
      renderer.renderSupplementation(calculatedSupplementationData);
      renderer.renderVaccines(calculatedVaccineData);
      renderer.renderFooter();

      // 7. Salvar PDF
      const fileName = PDFUtils.generateFileName(patientData.name);
      pdf.save(fileName);

    } catch (error) {
      console.error('Erro ao gerar PDF:', error);
      alert(`Erro ao gerar PDF: ${error.message || 'Erro desconhecido'}. Tente novamente.`);
    }
  };

  return (
    <div className="flex gap-4 justify-center">
      <Button
        onClick={generatePDF}
        className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 group"
      >
        <FileText className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform" />
        <span className="font-medium">📄 Gerar Laudo PDF</span>
        <Download className="ml-2 h-4 w-4 group-hover:translate-y-[1px] transition-transform" />
      </Button>
    </div>
  );
}
