import React from 'react';
import { ResponsiveAd, BannerAd, SquareAd } from './AdSenseAd';
import { getAdSlot } from '@/config/adsense';

interface AdManagerProps {
  position: 'header' | 'sidebar' | 'content' | 'footer' | 'between-content';
  page?: string;
  className?: string;
  minHeight?: string;
}

/**
 * Gerenciador inteligente de anúncios
 * Define qual tipo de anúncio mostrar baseado na posição
 */
export const AdManager: React.FC<AdManagerProps> = ({
  position,
  page = 'generic',
  className = '',
  minHeight = '90px'
}) => {
  // Obter slot dinâmico baseado na página e posição
  const adSlot = getAdSlot(page, position);

  // Configurações por posição
  const getAdComponent = () => {
    switch (position) {
      case 'header':
        return (
          <BannerAd
            adSlot={adSlot}
            className={`ad-header ${className}`}
          />
        );

      case 'sidebar':
        return (
          <SquareAd
            adSlot={adSlot}
            className={`ad-sidebar ${className}`}
          />
        );

      case 'content':
      case 'between-content':
        return (
          <ResponsiveAd
            adSlot={adSlot}
            className={`ad-content ${className}`}
            minHeight={minHeight}
          />
        );

      case 'footer':
        return (
          <BannerAd
            adSlot={adSlot}
            className={`ad-footer ${className}`}
          />
        );

      default:
        return (
          <ResponsiveAd
            adSlot={adSlot}
            className={className}
            minHeight={minHeight}
          />
        );
    }
  };

  return (
    <div className={`ad-container ad-${position} ${className}`}>
      {/* Label para identificar anúncio (obrigatório em alguns países) */}
      <div className="ad-label text-xs text-gray-500 text-center mb-1">
        Publicidade
      </div>
      
      {getAdComponent()}
    </div>
  );
};

/**
 * Componente para anúncio entre conteúdos
 * Usado entre cards, posts, etc.
 */
export const ContentAd: React.FC<{ className?: string }> = ({ className = '' }) => {
  return (
    <div className={`my-6 ${className}`}>
      <AdManager 
        position="between-content" 
        className="rounded-lg border border-gray-200 dark:border-gray-700 p-4"
        minHeight="120px"
      />
    </div>
  );
};

/**
 * Componente para anúncio na sidebar
 */
export const SidebarAd: React.FC<{ className?: string }> = ({ className = '' }) => {
  return (
    <div className={`mb-6 ${className}`}>
      <AdManager 
        position="sidebar" 
        className="rounded-lg border border-gray-200 dark:border-gray-700 p-4"
      />
    </div>
  );
};

/**
 * Componente para anúncio no header
 */
export const HeaderAd: React.FC<{ className?: string }> = ({ className = '' }) => {
  return (
    <div className={`w-full ${className}`}>
      <AdManager 
        position="header" 
        className="bg-gray-50 dark:bg-gray-800 rounded-lg p-2"
      />
    </div>
  );
};

/**
 * Componente para anúncio no footer
 */
export const FooterAd: React.FC<{ className?: string }> = ({ className = '' }) => {
  return (
    <div className={`w-full mt-8 ${className}`}>
      <AdManager 
        position="footer" 
        className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4"
      />
    </div>
  );
};
