import HelmetWrapper from "@/components/utils/HelmetWrapper";

interface ICDSEOProps {
  title: string;
  description: string;
  slug: string;
  keywords: string[];
  clinicalUse: string;
  features: string[];
  benefits: string[];
  clinicalSignificance: string;
  relatedTopics: string[];
  searchType?: 'main' | 'category' | 'specific';
  categoryName?: string;
  icdCode?: string;
}

export const ICDSEO = ({
  title,
  description,
  slug,
  keywords,
  clinicalUse,
  features,
  benefits,
  clinicalSignificance,
  relatedTopics,
  searchType = 'main',
  categoryName,
  icdCode
}: ICDSEOProps) => {

  // Gerar título dinâmico otimizado
  const generateTitle = () => {
    let seoTitle = `${title}`;
    
    if (searchType === 'main') {
      seoTitle += ` - Busca CID-10 Pediátrica`;
    } else if (searchType === 'category') {
      seoTitle += ` - Categoria CID-10`;
    } else if (searchType === 'specific') {
      seoTitle += ` - Código CID-10`;
    }
    
    seoTitle += ` | PedBook`;
    
    return seoTitle.substring(0, 60);
  };

  // Gerar descrição dinâmica
  const generateDescription = () => {
    let desc = `${title}: ${description}`;
    
    if (icdCode) {
      desc += ` Código: ${icdCode}.`;
    }
    
    if (categoryName) {
      desc += ` Categoria: ${categoryName}.`;
    }
    
    desc += ` ${clinicalUse}`;
    
    if (features.length > 0) {
      desc += ` Inclui: ${features.slice(0, 3).join(', ')}.`;
    }
    
    desc += ` Ferramenta essencial para codificação médica pediátrica.`;
    
    return desc.substring(0, 160);
  };

  // Gerar keywords dinâmicas
  const generateKeywords = () => {
    const baseKeywords = [
      `${title.toLowerCase()}`,
      `cid-10 ${title.toLowerCase()}`,
      `${title.toLowerCase()} pediatria`,
      `código ${title.toLowerCase()}`,
      `classificação ${title.toLowerCase()}`,
      `busca cid ${title.toLowerCase()}`
    ];

    // Adicionar código específico se disponível
    if (icdCode) {
      baseKeywords.push(
        `${icdCode}`,
        `código ${icdCode}`,
        `cid ${icdCode}`,
        `${icdCode} pediatria`
      );
    }

    // Adicionar categoria se disponível
    if (categoryName) {
      baseKeywords.push(
        `${categoryName.toLowerCase()}`,
        `cid ${categoryName.toLowerCase()}`,
        `categoria ${categoryName.toLowerCase()}`
      );
    }

    // Adicionar keywords por tipo de busca
    if (searchType === 'main') {
      baseKeywords.push(
        'cid-10 pediatria',
        'busca cid',
        'códigos cid',
        'classificação doenças',
        'cid pediátrico',
        'busca diagnóstico',
        'códigos médicos'
      );
    }

    if (searchType === 'category') {
      baseKeywords.push(
        'categorias cid',
        'grupos cid',
        'classificação médica',
        'capítulos cid',
        'organização cid'
      );
    }

    if (searchType === 'specific') {
      baseKeywords.push(
        'código específico',
        'diagnóstico cid',
        'doença cid',
        'condição médica',
        'patologia cid'
      );
    }

    // Adicionar keywords específicas passadas
    baseKeywords.push(...keywords);

    // Adicionar features como keywords
    features.forEach(feature => {
      baseKeywords.push(`${feature.toLowerCase()}`);
    });

    // Adicionar tópicos relacionados
    relatedTopics.forEach(topic => {
      baseKeywords.push(`${topic.toLowerCase()}`);
    });

    return baseKeywords.join(", ");
  };

  // URL canônica
  const canonicalUrl = `https://pedb.com.br/icd`;

  // Schema.org para ferramenta médica
  const medicalToolSchema = {
    "@context": "https://schema.org",
    "@type": "MedicalWebPage",
    "name": generateTitle(),
    "description": generateDescription(),
    "url": canonicalUrl,
    "mainContentOfPage": {
      "@type": "WebPageElement",
      "cssSelector": "main"
    },
    "specialty": "Codificação Médica",
    "audience": {
      "@type": "MedicalAudience",
      "audienceType": "Médicos pediatras e profissionais da saúde"
    },
    "about": {
      "@type": "MedicalCondition",
      "name": title,
      "description": clinicalUse,
      "code": icdCode ? {
        "@type": "MedicalCode",
        "code": icdCode,
        "codingSystem": "ICD-10"
      } : undefined
    },
    "lastReviewed": new Date().toISOString().split('T')[0],
    "reviewedBy": {
      "@type": "Organization",
      "name": "PedBook",
      "url": "https://pedb.com.br"
    }
  };

  // Schema.org para organização médica
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "MedicalOrganization",
    "name": "PedBook",
    "url": "https://pedb.com.br",
    "logo": {
      "@type": "ImageObject",
      "url": "https://pedb.com.br/faviconx.webp"
    },
    "medicalSpecialty": "Pediatria",
    "serviceType": "Codificação CID-10",
    "areaServed": "Brasil",
    "availableService": {
      "@type": "MedicalTherapy",
      "name": title,
      "description": clinicalUse
    }
  };

  // Breadcrumb Schema
  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "PedBook",
        "item": "https://pedb.com.br"
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "CID-10",
        "item": "https://pedb.com.br/icd"
      }
    ]
  };

  return (
    <HelmetWrapper>
      {/* Título e Descrição Dinâmicos */}
      <title>{generateTitle()}</title>
      <meta name="description" content={generateDescription()} />
      <meta name="keywords" content={generateKeywords()} />

      {/* Meta tags médicas específicas */}
      <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1" />
      <meta name="googlebot" content="index, follow" />
      <meta name="medical-content" content="medical-coding" />
      <meta name="target-audience" content="healthcare-professionals" />
      <meta name="content-type" content="medical-classification" />
      <meta name="clinical-specialty" content="pediatrics" />
      <meta name="coding-system" content="ICD-10" />
      
      {/* Geo targeting */}
      <meta name="geo.region" content="BR" />
      <meta name="geo.country" content="Brazil" />
      <meta name="language" content="Portuguese" />

      {/* Open Graph */}
      <meta property="og:title" content={generateTitle()} />
      <meta property="og:description" content={generateDescription()} />
      <meta property="og:type" content="website" />
      <meta property="og:url" content={canonicalUrl} />
      <meta property="og:image" content="https://pedb.com.br/faviconx.webp" />
      <meta property="og:image:alt" content={`${title} - PedBook`} />
      <meta property="og:site_name" content="PedBook" />
      <meta property="og:locale" content="pt_BR" />
      <meta property="article:section" content="Medicina" />
      <meta property="article:tag" content="CID-10" />
      <meta property="article:tag" content="Codificação Médica" />
      <meta property="article:tag" content={searchType} />
      {relatedTopics.map((topic, index) => (
        <meta key={index} property="article:tag" content={topic} />
      ))}

      {/* Twitter Cards */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={generateTitle()} />
      <meta name="twitter:description" content={generateDescription()} />
      <meta name="twitter:image" content="https://pedb.com.br/faviconx.webp" />
      <meta name="twitter:site" content="@pedbook" />

      {/* Canonical */}
      <link rel="canonical" href={canonicalUrl} />

      {/* Schema.org - Ferramenta Médica */}
      <script type="application/ld+json">
        {JSON.stringify(medicalToolSchema)}
      </script>

      {/* Schema.org - Organização Médica */}
      <script type="application/ld+json">
        {JSON.stringify(organizationSchema)}
      </script>

      {/* Schema.org - Breadcrumb */}
      <script type="application/ld+json">
        {JSON.stringify(breadcrumbSchema)}
      </script>
    </HelmetWrapper>
  );
};
