import React from 'react';
import { CommandGroup, CommandItem } from "@/components/ui/command";
import { MessageCircle, Bot } from "lucide-react";
import { useNavigate } from 'react-router-dom';

interface DrWillSuggestionProps {
  searchTerm: string;
  onSelect?: () => void;
}

export const DrWillSuggestion = ({ searchTerm, onSelect }: DrWillSuggestionProps) => {
  const navigate = useNavigate();

  const handleAskWill = () => {
    if (onSelect) {
      onSelect(); // Fechar o search
    }
    
    // Navegar para o Dr. Will com o termo de busca
    navigate('/dr-will', { 
      state: { 
        initialMessage: `Olá Dr. Will! Estava procurando por "${searchTerm}" no PedBook mas não encontrei exatamente o que preciso. Pode me ajudar?` 
      } 
    });
  };

  return (
    <CommandGroup className="px-0">
      <div className="sticky top-0 z-10 bg-gradient-to-r from-indigo-50 to-blue-50 dark:from-indigo-900/20 dark:to-blue-900/20 backdrop-blur-md px-4 py-2 text-xs font-medium text-indigo-700 dark:text-indigo-300 border-b border-indigo-200/50 dark:border-indigo-700/50">
        <div className="flex items-center gap-2">
          <Bot className="h-4 w-4" />
          Perguntar ao Dr. Will
        </div>
      </div>
      
      <div className="flex flex-col">
        <CommandItem
          onSelect={handleAskWill}
          className="flex items-start px-4 py-4 hover:bg-indigo-50/80 dark:hover:bg-indigo-900/20 active:bg-indigo-100 dark:active:bg-indigo-800/30 cursor-pointer transition-colors rounded-md m-1 border border-indigo-200/30 dark:border-indigo-700/30"
        >
          {/* Ícone */}
          <div className="flex-shrink-0 mr-3 mt-0.5 bg-indigo-100/80 dark:bg-indigo-900/40 p-1.5 rounded-md">
            <MessageCircle className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
          </div>

          {/* Conteúdo */}
          <div className="flex flex-col flex-1 min-w-0">
            <div className="font-medium text-gray-800 dark:text-gray-200 flex items-center gap-2">
              Não encontrou o que procurava?
            </div>
            
            <div className="flex flex-wrap items-center gap-x-2 mt-1">
              <p className="text-sm text-gray-600 dark:text-gray-300">
                Pergunte ao Dr. Will sobre <span className="font-medium text-indigo-600 dark:text-indigo-400">"{searchTerm}"</span>
              </p>
            </div>
            
            <div className="mt-2">
              <span className="inline-flex items-center gap-1 bg-indigo-100 dark:bg-indigo-900/40 text-indigo-800 dark:text-indigo-300 text-xs font-medium px-2 py-1 rounded-md">
                <Bot className="h-3 w-3" />
                Assistente IA
              </span>
            </div>
          </div>
        </CommandItem>
      </div>
    </CommandGroup>
  );
};
