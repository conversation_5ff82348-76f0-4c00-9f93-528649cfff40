export interface FlowchartSEOData {
  title: string;
  description: string;
  slug: string;
  category: 'emergency' | 'venomous' | 'general';
  subcategory?: string;
  keywords: string[];
  clinicalCondition: string;
  targetAge: 'pediatric' | 'all';
  urgencyLevel: 'emergency' | 'urgent' | 'routine';
  symptoms: string[];
  treatment: string[];
  parentPath?: string;
}

export const FLOWCHART_SEO_DATA: Record<string, FlowchartSEOData> = {
  // Fluxogramas de Emergência
  'seizure': {
    title: 'Crise Convulsiva',
    description: 'Protocolo para manejo de crises convulsivas e status epilepticus em pediatria com algoritmo passo a passo',
    slug: 'seizure',
    category: 'emergency',
    keywords: ['convulsão', 'epilepsia', 'status epilepticus', 'benzodiazepina', 'fenobarbital'],
    clinicalCondition: 'Sistema Nervoso Central',
    targetAge: 'pediatric',
    urgencyLevel: 'emergency',
    symptoms: ['convulsão', 'perda de consciência', 'movimentos tônico-clônicos', 'cianose'],
    treatment: ['benzodiazepina', 'fenobarbital', 'fenitoína', 'suporte ventilatório']
  },

  'dengue': {
    title: 'Dengue',
    description: 'Fluxograma para diagnóstico e manejo de casos suspeitos de dengue em pediatria',
    slug: 'dengue',
    category: 'emergency',
    keywords: ['dengue', 'febre hemorrágica', 'aedes aegypti', 'plaquetopenia', 'choque'],
    clinicalCondition: 'Doenças Infecciosas',
    targetAge: 'pediatric',
    urgencyLevel: 'urgent',
    symptoms: ['febre', 'cefaleia', 'mialgia', 'exantema', 'dor abdominal'],
    treatment: ['hidratação', 'paracetamol', 'monitoramento', 'suporte hemodinâmico']
  },

  'dka': {
    title: 'Cetoacidose Diabética',
    description: 'Protocolo para manejo de cetoacidose diabética em pediatria com cálculo de hidratação e insulina',
    slug: 'dka',
    category: 'emergency',
    keywords: ['cetoacidose', 'diabetes', 'insulina', 'acidose', 'desidratação'],
    clinicalCondition: 'Sistema Endócrino',
    targetAge: 'pediatric',
    urgencyLevel: 'emergency',
    symptoms: ['hiperglicemia', 'cetose', 'acidose', 'desidratação', 'vômitos'],
    treatment: ['insulina', 'hidratação', 'correção eletrolítica', 'bicarbonato']
  },

  'anaphylaxis': {
    title: 'Anafilaxia',
    description: 'Protocolo de emergência para manejo de anafilaxia em pediatria com doses de adrenalina',
    slug: 'anaphylaxis',
    category: 'emergency',
    keywords: ['anafilaxia', 'adrenalina', 'epinefrina', 'choque anafilático', 'alergia'],
    clinicalCondition: 'Sistema Imunológico',
    targetAge: 'pediatric',
    urgencyLevel: 'emergency',
    symptoms: ['urticária', 'angioedema', 'broncoespasmo', 'hipotensão', 'choque'],
    treatment: ['adrenalina', 'corticoide', 'anti-histamínico', 'broncodilatador']
  },

  'asthma': {
    title: 'Crise Asmática',
    description: 'Fluxograma para manejo de crise asmática em pediatria com classificação de gravidade',
    slug: 'asthma',
    category: 'emergency',
    keywords: ['asma', 'broncoespasmo', 'salbutamol', 'corticoide', 'nebulização'],
    clinicalCondition: 'Sistema Respiratório',
    targetAge: 'pediatric',
    urgencyLevel: 'urgent',
    symptoms: ['dispneia', 'sibilos', 'tosse', 'tiragem', 'cianose'],
    treatment: ['salbutamol', 'corticoide', 'oxigênio', 'ipratrópio']
  },

  'pecarn': {
    title: 'PECARN - Trauma Craniano',
    description: 'Protocolo PECARN para avaliação de trauma craniano em pediatria e indicação de tomografia',
    slug: 'pecarn',
    category: 'emergency',
    keywords: ['PECARN', 'trauma craniano', 'tomografia', 'TCE', 'neurologia'],
    clinicalCondition: 'Sistema Nervoso Central',
    targetAge: 'pediatric',
    urgencyLevel: 'urgent',
    symptoms: ['trauma craniano', 'alteração consciência', 'vômitos', 'cefaleia'],
    treatment: ['observação', 'tomografia', 'neurocirurgia', 'monitoramento']
  },

  'hidratacao': {
    title: 'Hidratação Venosa',
    description: 'Calculadora para hidratação venosa de manutenção em pediatria usando método Holliday-Segar',
    slug: 'hidratacao',
    category: 'general',
    keywords: ['hidratação', 'Holliday-Segar', 'soro', 'eletrólitos', 'manutenção'],
    clinicalCondition: 'Equilíbrio Hidroeletrolítico',
    targetAge: 'pediatric',
    urgencyLevel: 'routine',
    symptoms: ['desidratação', 'vômitos', 'diarreia', 'febre'],
    treatment: ['soro fisiológico', 'soro glicosado', 'eletrólitos', 'potássio']
  },

  // Fluxogramas de Animais Peçonhentos
  'scorpion': {
    title: 'Acidente Escorpiônico',
    description: 'Protocolo para manejo de acidentes escorpiônicos em pediatria com classificação de gravidade e antídoto',
    slug: 'scorpion',
    category: 'venomous',
    subcategory: 'Animais Peçonhentos',
    keywords: ['escorpião', 'Tityus', 'soro antiescorpiônico', 'envenenamento', 'neurotoxina'],
    clinicalCondition: 'Toxicologia',
    targetAge: 'pediatric',
    urgencyLevel: 'urgent',
    symptoms: ['dor local', 'parestesia', 'sudorese', 'sialorreia', 'agitação'],
    treatment: ['soro antiescorpiônico', 'analgesia', 'sedação', 'suporte'],
    parentPath: 'venomous'
  },

  'bothropic': {
    title: 'Acidente Botrópico',
    description: 'Protocolo para manejo de acidentes botrópicos (jararaca) em pediatria com soro antibotrópico',
    slug: 'bothropic',
    category: 'venomous',
    subcategory: 'Animais Peçonhentos',
    keywords: ['jararaca', 'botrópico', 'soro antibotrópico', 'coagulopatia', 'necrose'],
    clinicalCondition: 'Toxicologia',
    targetAge: 'pediatric',
    urgencyLevel: 'urgent',
    symptoms: ['dor local', 'edema', 'equimose', 'sangramento', 'necrose'],
    treatment: ['soro antibotrópico', 'analgesia', 'antibiótico', 'curativo'],
    parentPath: 'venomous'
  },

  'crotalic': {
    title: 'Acidente Crotálico',
    description: 'Protocolo para manejo de acidentes crotálicos (cascavel) em pediatria com soro anticrotálico',
    slug: 'crotalic',
    category: 'venomous',
    subcategory: 'Animais Peçonhentos',
    keywords: ['cascavel', 'crotálico', 'soro anticrotálico', 'miotoxina', 'nefrotoxina'],
    clinicalCondition: 'Toxicologia',
    targetAge: 'pediatric',
    urgencyLevel: 'urgent',
    symptoms: ['dor local', 'ptose palpebral', 'mialgia', 'urina escura', 'oligúria'],
    treatment: ['soro anticrotálico', 'hidratação', 'alcalinização', 'hemodiálise'],
    parentPath: 'venomous'
  },

  'elapidic': {
    title: 'Acidente Elapídico',
    description: 'Protocolo para manejo de acidentes elapídicos (coral) em pediatria com soro antielapídico',
    slug: 'elapidic',
    category: 'venomous',
    subcategory: 'Animais Peçonhentos',
    keywords: ['coral', 'elapídico', 'soro antielapídico', 'neurotoxina', 'paralisia'],
    clinicalCondition: 'Toxicologia',
    targetAge: 'pediatric',
    urgencyLevel: 'urgent',
    symptoms: ['dor local', 'parestesia', 'diplopia', 'disfagia', 'paralisia'],
    treatment: ['soro antielapídico', 'suporte ventilatório', 'fisioterapia'],
    parentPath: 'venomous'
  },

  'phoneutria': {
    title: 'Acidente Fonêutrico',
    description: 'Protocolo para manejo de acidentes com aranha armadeira em pediatria',
    slug: 'phoneutria',
    category: 'venomous',
    subcategory: 'Animais Peçonhentos',
    keywords: ['aranha armadeira', 'Phoneutria', 'priapismo', 'neurotoxina'],
    clinicalCondition: 'Toxicologia',
    targetAge: 'pediatric',
    urgencyLevel: 'urgent',
    symptoms: ['dor local', 'sudorese', 'sialorreia', 'priapismo', 'agitação'],
    treatment: ['analgesia', 'sedação', 'bloqueador', 'suporte'],
    parentPath: 'venomous'
  },

  'loxoscelic': {
    title: 'Acidente Loxoscélico',
    description: 'Protocolo para manejo de acidentes com aranha marrom em pediatria',
    slug: 'loxoscelic',
    category: 'venomous',
    subcategory: 'Animais Peçonhentos',
    keywords: ['aranha marrom', 'Loxosceles', 'necrose', 'hemólise', 'loxoscelismo'],
    clinicalCondition: 'Toxicologia',
    targetAge: 'pediatric',
    urgencyLevel: 'urgent',
    symptoms: ['dor local', 'necrose', 'hemólise', 'icterícia', 'anemia'],
    treatment: ['corticoide', 'antibiótico', 'curativo', 'transfusão'],
    parentPath: 'venomous'
  }
};
