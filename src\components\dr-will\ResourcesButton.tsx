import React from 'react';
import { Button } from '@/components/ui/button';

interface ResourcesButtonProps {
  count: number;
  onClick: () => void;
}

export const ResourcesButton: React.FC<ResourcesButtonProps> = ({ count, onClick }) => {
  return (
    <div className="mt-5 pt-5 border-t-2 border-gray-200 dark:border-gray-700">
      <div className="text-center">
        <Button
          onClick={onClick}
          className="inline-flex items-center gap-3 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white border-none rounded-xl px-6 py-3 text-sm font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 active:scale-95"
        >
          <span className="text-lg">🔗</span>
          Acessar recursos encontrados ({count})
        </Button>
        <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
          💡 Recursos internos da plataforma PedBook
        </div>
      </div>
    </div>
  );
};
