import React from "react";
import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent } from "@/components/ui/card";

interface LoadingStatesProps {
  type: 'structure' | 'list' | 'search' | 'details' | 'categories';
  count?: number;
}

interface ProgressIndicatorProps {
  message: string;
  progress?: number;
  showSpinner?: boolean;
}

/**
 * Componente de indicador de progresso
 */
export const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({ 
  message, 
  progress, 
  showSpinner = true 
}) => (
  <div className="flex items-center justify-center gap-3 py-8">
    {showSpinner && (
      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
    )}
    <div className="text-center">
      <span className="text-lg text-gray-600 dark:text-gray-400">{message}</span>
      {progress !== undefined && (
        <div className="mt-2 w-48 bg-gray-200 rounded-full h-2 dark:bg-gray-700">
          <div 
            className="bg-blue-500 h-2 rounded-full transition-all duration-300" 
            style={{ width: `${progress}%` }}
          ></div>
        </div>
      )}
    </div>
  </div>
);

/**
 * Skeleton para estrutura completa (carregamento inicial)
 */
export const StructureSkeleton: React.FC = () => (
  <div className="space-y-6">
    <ProgressIndicator message="Carregando medicamentos..." />
    
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {Array.from({ length: 6 }).map((_, i) => (
        <Card key={i} className="animate-pulse">
          <CardContent className="p-4">
            <div className="flex items-start gap-3 mb-3">
              <Skeleton className="h-10 w-10 rounded-lg" />
              <div className="flex-1">
                <Skeleton className="h-5 w-3/4 mb-2" />
                <Skeleton className="h-4 w-1/2" />
              </div>
            </div>
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-4 w-2/3" />
            <div className="flex gap-2 mt-3">
              <Skeleton className="h-6 w-16" />
              <Skeleton className="h-6 w-20" />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
    
    <div className="text-center">
      <div className="inline-flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
        <div className="animate-pulse h-2 w-2 bg-blue-500 rounded-full"></div>
        <span>Organizando medicamentos e categorias...</span>
      </div>
    </div>
  </div>
);

/**
 * Skeleton para lista de medicamentos
 */
export const MedicationListSkeleton: React.FC<{ count?: number }> = ({ count = 8 }) => (
  <div className="space-y-4">
    <ProgressIndicator message="Carregando lista de medicamentos..." showSpinner={true} />
    
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      {Array.from({ length: count }).map((_, i) => (
        <Card key={i} className="animate-pulse border-2">
          <CardContent className="p-4">
            <div className="flex items-center gap-3 mb-3">
              <Skeleton className="h-8 w-8 rounded-lg" />
              <div className="flex-1">
                <Skeleton className="h-5 w-3/4 mb-1" />
                <Skeleton className="h-3 w-1/2" />
              </div>
              <Skeleton className="h-4 w-4" />
            </div>
            <Skeleton className="h-3 w-full mb-2" />
            <Skeleton className="h-3 w-4/5 mb-3" />
            
            <div className="flex justify-between items-center">
              <div className="flex gap-1">
                <Skeleton className="h-5 w-12" />
                <Skeleton className="h-5 w-16" />
              </div>
              <Skeleton className="h-6 w-6 rounded" />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  </div>
);

/**
 * Skeleton para categorias
 */
export const CategoriesSkeleton: React.FC<{ count?: number }> = ({ count = 6 }) => (
  <div className="space-y-4">
    <ProgressIndicator message="Carregando categorias..." />
    
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
      {Array.from({ length: count }).map((_, i) => (
        <Card key={i} className="animate-pulse border-2 border-green-200 dark:border-green-700">
          <CardContent className="p-3 text-center">
            <Skeleton className="h-8 w-8 rounded-lg mx-auto mb-2 bg-green-200 dark:bg-green-800" />
            <Skeleton className="h-4 w-3/4 mx-auto mb-1" />
            <Skeleton className="h-3 w-1/2 mx-auto" />
          </CardContent>
        </Card>
      ))}
    </div>
  </div>
);

/**
 * Skeleton para resultados de busca
 */
export const SearchSkeleton: React.FC<{ count?: number }> = ({ count = 4 }) => (
  <div className="space-y-4">
    <div className="flex items-center justify-center gap-3 py-6">
      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-purple-500"></div>
      <span className="text-purple-600 dark:text-purple-400">Pesquisando medicamentos...</span>
    </div>
    
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {Array.from({ length: count }).map((_, i) => (
        <Card key={i} className="animate-pulse border-purple-200 dark:border-purple-700">
          <CardContent className="p-4">
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center gap-3 flex-1">
                <Skeleton className="h-8 w-8 rounded-full bg-purple-200 dark:bg-purple-800" />
                <div className="flex-1">
                  <Skeleton className="h-5 w-3/4 mb-2" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              </div>
              <div className="text-right">
                <Skeleton className="h-4 w-12 mb-1" />
                <Skeleton className="h-3 w-8" />
              </div>
            </div>
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-4 w-2/3" />
            
            <div className="flex justify-between items-center mt-3">
              <Skeleton className="h-5 w-20" />
              <Skeleton className="h-6 w-16" />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  </div>
);

/**
 * Skeleton para detalhes de medicamento
 */
export const MedicationDetailsSkeleton: React.FC = () => (
  <div className="space-y-6">
    <ProgressIndicator message="Carregando detalhes do medicamento..." />
    
    {/* Header */}
    <Card className="animate-pulse">
      <CardContent className="p-6">
        <div className="flex items-start gap-4 mb-4">
          <Skeleton className="h-12 w-12 rounded-lg" />
          <div className="flex-1">
            <Skeleton className="h-8 w-1/2 mb-2" />
            <Skeleton className="h-5 w-1/3 mb-2" />
            <Skeleton className="h-4 w-2/3" />
          </div>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <div key={i} className="text-center">
              <Skeleton className="h-6 w-full mb-1" />
              <Skeleton className="h-4 w-3/4 mx-auto" />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
    
    {/* Casos de uso */}
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {Array.from({ length: 4 }).map((_, i) => (
        <Card key={i} className="animate-pulse">
          <CardContent className="p-4">
            <Skeleton className="h-6 w-1/2 mb-3" />
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-4 w-3/4 mb-4" />
            
            <div className="space-y-2">
              {Array.from({ length: 3 }).map((_, j) => (
                <div key={j} className="flex justify-between">
                  <Skeleton className="h-4 w-1/3" />
                  <Skeleton className="h-4 w-1/4" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  </div>
);

/**
 * Componente principal que escolhe o skeleton apropriado
 */
export const MedicationSkeletons: React.FC<LoadingStatesProps> = ({ type, count }) => {
  switch (type) {
    case 'structure':
      return <StructureSkeleton />;
    case 'list':
      return <MedicationListSkeleton count={count} />;
    case 'categories':
      return <CategoriesSkeleton count={count} />;
    case 'search':
      return <SearchSkeleton count={count} />;
    case 'details':
      return <MedicationDetailsSkeleton />;
    default:
      return <StructureSkeleton />;
  }
};

/**
 * Mensagens de loading específicas
 */
export const LoadingMessages = {
  STRUCTURE: "Carregando medicamentos...",
  LIST: "Carregando lista de medicamentos...",
  CATEGORIES: "Carregando categorias...",
  SEARCH: "Pesquisando medicamentos...",
  DETAILS: "Carregando detalhes do medicamento...",
  ORGANIZING: "Organizando dados...",
  OPTIMIZING: "Otimizando carregamento...",
} as const;
