// Cálculo de percentis exatos baseado nas tabelas OMS do Supabase
// Integração com a estrutura existente de growth_curve_metadata

import { supabase } from "@/integrations/supabase/client";

export interface PercentileResult {
  percentile: number;
  zScore: number;
  interpretation: "muito_baixo" | "baixo" | "normal" | "alto" | "muito_alto";
  description: string;
  color: string;
}

interface GrowthCurveDataPoint {
  age_months: number;
  L: number;
  M: number;
  S: number;
  percentiles: {
    "3rd": number;
    "15th": number;
    "50th": number;
    "85th": number;
    "97th": number;
  };
}

// Cache para dados WHO
let whoDataCache: {
  [key: string]: GrowthCurveDataPoint[];
} = {};

// Buscar dados WHO do Supabase
async function fetchWHOData(gender: "male" | "female", type: "weight" | "height" | "head-circumference"): Promise<GrowthCurveDataPoint[]> {
  const cacheKey = `${gender}-${type}`;

  // Verificar cache primeiro
  if (whoDataCache[cacheKey]) {
    return whoDataCache[cacheKey];
  }

  try {
    const { data, error } = await supabase
      .from('pedbook_growth_curve_metadata')
      .select('data')
      .eq('gender', gender)
      .eq('type', type)
      .single();

    if (error) {
      console.error('Erro ao buscar dados WHO:', error);
      return [];
    }

    const growthData = (data?.data as any[]) || [];

    // Processar e cachear os dados
    const processedData = growthData.map(point => ({
      age_months: point.age_months,
      L: point.L,
      M: point.M,
      S: point.S,
      percentiles: {
        "3rd": point.percentiles["3rd"],
        "15th": point.percentiles["15th"],
        "50th": point.percentiles["50th"],
        "85th": point.percentiles["85th"],
        "97th": point.percentiles["97th"]
      }
    }));

    whoDataCache[cacheKey] = processedData;
    return processedData;
  } catch (error) {
    console.error('Erro ao processar dados WHO:', error);
    return [];
  }
}

// Interpolação linear para idades intermediárias
function interpolateDataPoint(age: number, data: GrowthCurveDataPoint[]): GrowthCurveDataPoint | null {
  if (!data || data.length === 0) return null;

  // Ordenar por idade
  const sortedData = data.sort((a, b) => a.age_months - b.age_months);

  // Se a idade é exata, retornar o ponto direto
  const exactMatch = sortedData.find(point => point.age_months === age);
  if (exactMatch) return exactMatch;

  // Encontrar pontos adjacentes para interpolação
  let lowerPoint: GrowthCurveDataPoint | null = null;
  let upperPoint: GrowthCurveDataPoint | null = null;

  for (let i = 0; i < sortedData.length - 1; i++) {
    if (age >= sortedData[i].age_months && age <= sortedData[i + 1].age_months) {
      lowerPoint = sortedData[i];
      upperPoint = sortedData[i + 1];
      break;
    }
  }

  // Se está fora do range, usar os valores extremos
  if (age <= sortedData[0].age_months) return sortedData[0];
  if (age >= sortedData[sortedData.length - 1].age_months) return sortedData[sortedData.length - 1];

  // Se não encontrou pontos adjacentes, retornar null
  if (!lowerPoint || !upperPoint) return null;

  // Interpolação linear
  const ratio = (age - lowerPoint.age_months) / (upperPoint.age_months - lowerPoint.age_months);

  return {
    age_months: age,
    L: lowerPoint.L + (upperPoint.L - lowerPoint.L) * ratio,
    M: lowerPoint.M + (upperPoint.M - lowerPoint.M) * ratio,
    S: lowerPoint.S + (upperPoint.S - lowerPoint.S) * ratio,
    percentiles: {
      "3rd": lowerPoint.percentiles["3rd"] + (upperPoint.percentiles["3rd"] - lowerPoint.percentiles["3rd"]) * ratio,
      "15th": lowerPoint.percentiles["15th"] + (upperPoint.percentiles["15th"] - lowerPoint.percentiles["15th"]) * ratio,
      "50th": lowerPoint.percentiles["50th"] + (upperPoint.percentiles["50th"] - lowerPoint.percentiles["50th"]) * ratio,
      "85th": lowerPoint.percentiles["85th"] + (upperPoint.percentiles["85th"] - lowerPoint.percentiles["85th"]) * ratio,
      "97th": lowerPoint.percentiles["97th"] + (upperPoint.percentiles["97th"] - lowerPoint.percentiles["97th"]) * ratio
    }
  };
}

function calculateZScore(value: number, percentiles: number[]): number {
  // Aproximação usando os percentis conhecidos
  // P3 ≈ Z-2, P15 ≈ Z-1, P50 ≈ Z0, P85 ≈ Z+1, P97 ≈ Z+2
  const [p3, p15, p50, p85, p97] = percentiles;
  
  if (value <= p3) return -2 - (p3 - value) / (p3 * 0.1);
  if (value <= p15) return -2 + (value - p3) / (p15 - p3);
  if (value <= p50) return -1 + (value - p15) / (p50 - p15);
  if (value <= p85) return 0 + (value - p50) / (p85 - p50);
  if (value <= p97) return 1 + (value - p85) / (p97 - p85);
  
  return 2 + (value - p97) / (p97 * 0.1);
}

function zScoreToPercentile(zScore: number): number {
  // Aproximação da função de distribuição normal cumulativa
  // Fórmula simplificada para conversão Z-score → percentil
  if (zScore <= -3) return 0.1;
  if (zScore <= -2) return 2.3;
  if (zScore <= -1) return 15.9;
  if (zScore <= 0) return 50.0;
  if (zScore <= 1) return 84.1;
  if (zScore <= 2) return 97.7;
  if (zScore <= 3) return 99.9;
  
  // Interpolação para valores intermediários
  const zPoints = [-3, -2, -1, 0, 1, 2, 3];
  const pPoints = [0.1, 2.3, 15.9, 50.0, 84.1, 97.7, 99.9];
  
  for (let i = 0; i < zPoints.length - 1; i++) {
    if (zScore >= zPoints[i] && zScore <= zPoints[i + 1]) {
      const ratio = (zScore - zPoints[i]) / (zPoints[i + 1] - zPoints[i]);
      return pPoints[i] + (pPoints[i + 1] - pPoints[i]) * ratio;
    }
  }
  
  return zScore > 0 ? 99.9 : 0.1;
}

function getInterpretation(percentile: number): PercentileResult['interpretation'] {
  // Classificação baseada nos padrões OMS/SBP para pediatria
  if (percentile < 0.5) return "muito_baixo";  // P<0.5: Extremamente baixo
  if (percentile < 3) return "muito_baixo";    // P0.5-P3: Muito baixo
  if (percentile < 10) return "baixo";         // P3-P10: Baixo
  if (percentile <= 90) return "normal";       // P10-P90: Normal
  if (percentile <= 97) return "alto";         // P90-P97: Alto
  return "muito_alto";                         // P>97: Muito alto
}

function getDescription(interpretation: PercentileResult['interpretation']): string {
  switch (interpretation) {
    case "muito_baixo": return "Muito baixo - Avaliação médica urgente";
    case "baixo": return "Baixo - Monitoramento e investigação";
    case "normal": return "Adequado - Crescimento normal";
    case "alto": return "Alto - Monitorar tendência";
    case "muito_alto": return "Muito alto - Investigação necessária";
  }
}

function getColor(interpretation: PercentileResult['interpretation']): string {
  switch (interpretation) {
    case "muito_baixo": return "text-red-600 bg-red-50 dark:bg-red-900/20 dark:text-red-400";
    case "baixo": return "text-orange-600 bg-orange-50 dark:bg-orange-900/20 dark:text-orange-400";
    case "normal": return "text-green-600 bg-green-50 dark:bg-green-900/20 dark:text-green-400";
    case "alto": return "text-blue-600 bg-blue-50 dark:bg-blue-900/20 dark:text-blue-400";
    case "muito_alto": return "text-purple-600 bg-purple-50 dark:bg-purple-900/20 dark:text-purple-400";
  }
}

// Função principal para calcular percentil exato usando dados do Supabase
export async function calculateExactPercentile(
  value: number,
  ageInMonths: number,
  gender: "male" | "female",
  measurement: "peso" | "altura" | "pc"
): Promise<PercentileResult> {
  try {
    // Mapear tipo de medida para o formato do Supabase
    const measurementType = measurement === "peso" ? "weight" :
                           measurement === "altura" ? "height" :
                           "head-circumference";

    // Buscar dados WHO do Supabase
    const whoData = await fetchWHOData(gender, measurementType);

    if (!whoData || whoData.length === 0) {
      // Fallback para valores padrão se não houver dados
      return {
        percentile: 50,
        zScore: 0,
        interpretation: "normal",
        description: "Dados não disponíveis",
        color: "text-gray-600 bg-gray-50 dark:bg-gray-900/20 dark:text-gray-400"
      };
    }

    // Interpolar dados para a idade específica
    const interpolatedData = interpolateDataPoint(ageInMonths, whoData);

    if (!interpolatedData) {
      return {
        percentile: 50,
        zScore: 0,
        interpretation: "normal",
        description: "Idade fora do range",
        color: "text-gray-600 bg-gray-50 dark:bg-gray-900/20 dark:text-gray-400"
      };
    }

    // Calcular Z-score usando a fórmula LMS da OMS
    const zScore = calculateLMSZScore(value, interpolatedData.L, interpolatedData.M, interpolatedData.S);

    // Converter Z-score para percentil
    const percentile = zScoreToPercentile(zScore);

    // Obter interpretação
    const interpretation = getInterpretation(percentile);
    const description = getDescription(interpretation);
    const color = getColor(interpretation);

    return {
      percentile: Math.round(percentile * 10) / 10,
      zScore: Math.round(zScore * 100) / 100,
      interpretation,
      description,
      color
    };
  } catch (error) {
    console.error('Erro ao calcular percentil:', error);
    return {
      percentile: 50,
      zScore: 0,
      interpretation: "normal",
      description: "Erro no cálculo",
      color: "text-gray-600 bg-gray-50 dark:bg-gray-900/20 dark:text-gray-400"
    };
  }
}

// Calcular Z-score usando a fórmula LMS da OMS
function calculateLMSZScore(value: number, L: number, M: number, S: number): number {
  if (L !== 0) {
    return (Math.pow(value / M, L) - 1) / (L * S);
  } else {
    return Math.log(value / M) / S;
  }
}

// Versão síncrona para compatibilidade (usa cache se disponível)
export function calculateExactPercentileSync(
  value: number,
  ageInMonths: number,
  gender: "male" | "female",
  measurement: "peso" | "altura" | "pc"
): PercentileResult {
  const measurementType = measurement === "peso" ? "weight" :
                         measurement === "altura" ? "height" :
                         "head-circumference";

  const cacheKey = `${gender}-${measurementType}`;
  const cachedData = whoDataCache[cacheKey];

  if (!cachedData) {
    return {
      percentile: 50,
      zScore: 0,
      interpretation: "normal",
      description: "Carregando dados...",
      color: "text-gray-600 bg-gray-50 dark:bg-gray-900/20 dark:text-gray-400"
    };
  }

  const interpolatedData = interpolateDataPoint(ageInMonths, cachedData);

  if (!interpolatedData) {
    return {
      percentile: 50,
      zScore: 0,
      interpretation: "normal",
      description: "Idade fora do range",
      color: "text-gray-600 bg-gray-50 dark:bg-gray-900/20 dark:text-gray-400"
    };
  }

  // O valor já vem na unidade correta (kg para peso, cm para altura/PC)
  const adjustedValue = value;

  const zScore = calculateLMSZScore(adjustedValue, interpolatedData.L, interpolatedData.M, interpolatedData.S);
  const percentile = zScoreToPercentile(zScore);

  const interpretation = getInterpretation(percentile);
  const description = getDescription(interpretation);
  const color = getColor(interpretation);

  return {
    percentile: Math.round(percentile * 10) / 10,
    zScore: Math.round(zScore * 100) / 100,
    interpretation,
    description,
    color
  };
}
