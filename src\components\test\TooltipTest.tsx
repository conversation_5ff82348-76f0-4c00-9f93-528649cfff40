import { formatAge, formatAgeCompact } from "@/utils/formatAge";

export function TooltipTest() {
  const testAges = [
    0.5,   // ~15 dias
    1.67,  // 1 mês e 20 dias
    2.33,  // 2 meses e 10 dias
    3.07,  // 3 meses e 2 dias (teste específico)
    6.5,   // 6 meses e 15 dias
    12.25, // 1 ano e 7 dias
    18.8,  // 1 ano, 6 meses e 24 dias
    24.0,  // 2 anos exatos
  ];

  return (
    <div className="p-6 space-y-4">
      <h2 className="text-xl font-bold">Teste de Formatação de Idade</h2>
      
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Formato Completo (Tooltips)</h3>
        {testAges.map((age, index) => (
          <div key={index} className="flex gap-4 p-2 bg-gray-50 rounded">
            <span className="font-mono w-16">{age}</span>
            <span className="font-medium">→</span>
            <span>{formatAge(age)}</span>
          </div>
        ))}
      </div>

      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Formato Compacto (Eixos)</h3>
        {testAges.map((age, index) => (
          <div key={index} className="flex gap-4 p-2 bg-blue-50 rounded">
            <span className="font-mono w-16">{age}</span>
            <span className="font-medium">→</span>
            <span>{formatAgeCompact(age)}</span>
          </div>
        ))}
      </div>

      <div className="mt-6 p-4 bg-green-50 rounded">
        <h3 className="text-lg font-semibold text-green-800">✅ Correção Implementada</h3>
        <p className="text-green-700">
          Agora os tooltips das curvas mostram a idade exata sem arredondamento!
        </p>
        <ul className="list-disc list-inside text-green-600 mt-2">
          <li><strong>Tooltips:</strong> Mostram idade completa (ex: "1 mês e 20 dias")</li>
          <li><strong>Eixos:</strong> Mostram formato compacto (ex: "1m20d")</li>
          <li><strong>Sem arredondamento:</strong> Precisão total mantida</li>
        </ul>
      </div>
    </div>
  );
}
