import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export interface PediDropFeedbackStats {
  total_feedback: number;
  continue_count: number;
  improve_count: number;
  discontinue_count: number;
  average_rating: number;
  feedback_by_post: Array<{
    post_id: string;
    feedback_count: number;
    average_rating: number;
    continue_count: number;
    improve_count: number;
    discontinue_count: number;
  }>;
  recent_comments: Array<{
    id: string;
    post_id: string;
    feedback_type: string;
    rating: number;
    message: string;
    created_at: string;
  }>;
}

/**
 * Hook para buscar estatísticas do feedback do PediDrop
 * Usa a tabela pedbook_feedbacks existente
 */
export function usePediDropFeedbackStats() {
  return useQuery({
    queryKey: ['pedidrop-feedback-stats'],
    queryFn: async (): Promise<PediDropFeedbackStats> => {
      // Buscar todos os feedbacks do PediDrop
      const { data: allFeedback, error: allError } = await supabase
        .from('pedbook_feedbacks')
        .select('*')
        .eq('feedback_category', 'pedidrop')
        .order('created_at', { ascending: false });

      if (allError) throw allError;

      // Calcular estatísticas gerais
      const totalFeedback = allFeedback?.length || 0;
      const continueCount = allFeedback?.filter(f => f.feedback_type === 'continue').length || 0;
      const improveCount = allFeedback?.filter(f => f.feedback_type === 'improve').length || 0;
      const discontinueCount = allFeedback?.filter(f => f.feedback_type === 'discontinue').length || 0;
      
      const averageRating = totalFeedback > 0 
        ? allFeedback.reduce((sum, f) => sum + (f.rating || 0), 0) / totalFeedback 
        : 0;

      // Agrupar feedback por post
      const feedbackByPost = allFeedback?.reduce((acc, feedback) => {
        const existing = acc.find(item => item.post_id === feedback.post_id);
        
        if (existing) {
          existing.feedback_count++;
          existing.total_rating += feedback.rating || 0;
          existing.average_rating = existing.total_rating / existing.feedback_count;
          
          if (feedback.feedback_type === 'continue') existing.continue_count++;
          else if (feedback.feedback_type === 'improve') existing.improve_count++;
          else if (feedback.feedback_type === 'discontinue') existing.discontinue_count++;
        } else {
          acc.push({
            post_id: feedback.post_id || 'general',
            feedback_count: 1,
            total_rating: feedback.rating || 0,
            average_rating: feedback.rating || 0,
            continue_count: feedback.feedback_type === 'continue' ? 1 : 0,
            improve_count: feedback.feedback_type === 'improve' ? 1 : 0,
            discontinue_count: feedback.feedback_type === 'discontinue' ? 1 : 0,
          });
        }
        
        return acc;
      }, [] as any[]) || [];

      // Ordenar por quantidade de feedback
      feedbackByPost.sort((a, b) => b.feedback_count - a.feedback_count);

      // Buscar comentários recentes (últimos 10 com comentário)
      const recentComments = allFeedback
        ?.filter(f => f.message && f.message.trim() !== '' && !f.message.startsWith('Feedback do PediDrop:'))
        .slice(0, 10)
        .map(f => ({
          id: f.id,
          post_id: f.post_id || 'general',
          feedback_type: f.feedback_type || 'unknown',
          rating: f.rating || 0,
          message: f.message || '',
          created_at: f.created_at,
        })) || [];

      return {
        total_feedback: totalFeedback,
        continue_count: continueCount,
        improve_count: improveCount,
        discontinue_count: discontinueCount,
        average_rating: Math.round(averageRating * 10) / 10, // Arredondar para 1 casa decimal
        feedback_by_post: feedbackByPost,
        recent_comments: recentComments,
      };
    },
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 30 * 60 * 1000, // 30 minutos
  });
}

/**
 * Hook para buscar feedback de um post específico do PediDrop
 */
export function usePediDropPostFeedback(postId: string) {
  return useQuery({
    queryKey: ['pedidrop-post-feedback', postId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('pedbook_feedbacks')
        .select('*')
        .eq('feedback_category', 'pedidrop')
        .eq('post_id', postId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      const totalFeedback = data?.length || 0;
      const continueCount = data?.filter(f => f.feedback_type === 'continue').length || 0;
      const improveCount = data?.filter(f => f.feedback_type === 'improve').length || 0;
      const discontinueCount = data?.filter(f => f.feedback_type === 'discontinue').length || 0;
      
      const averageRating = totalFeedback > 0 
        ? data.reduce((sum, f) => sum + (f.rating || 0), 0) / totalFeedback 
        : 0;

      return {
        feedbacks: data || [],
        stats: {
          total_feedback: totalFeedback,
          continue_count: continueCount,
          improve_count: improveCount,
          discontinue_count: discontinueCount,
          average_rating: Math.round(averageRating * 10) / 10,
        }
      };
    },
    enabled: !!postId,
    staleTime: 2 * 60 * 1000, // 2 minutos
    gcTime: 10 * 60 * 1000, // 10 minutos
  });
}
