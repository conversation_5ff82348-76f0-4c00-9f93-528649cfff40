import { useQuery, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { CACHE_STRATEGIES, CACHE_KEYS } from "@/utils/cacheConfig";

// Tipos de dados
interface Medication {
  id: string;
  name: string;
  compatibility_level: string;
  usage_description: string;
  additional_info?: string;
  efeitos_no_lactente?: string;
  alternativas_seguras?: string[];
  orientacoes_uso?: string;
  section_name?: string;
  subsection_name?: string;
}

interface Subsection {
  id: string;
  name: string;
  description?: string;
  medications?: Medication[];
  subsections?: Subsection[];
}

interface Section {
  id: string;
  name: string;
  description?: string;
  medications?: Medication[];
  subsections?: Subsection[];
}

interface BreastfeedingData {
  sections: Section[];
}

/**
 * Hook principal para estrutura completa de medicamentos de amamentação
 * Usa view materializada para performance otimizada
 */
export const useBreastfeedingStructure = () => {
  return useQuery({
    queryKey: CACHE_KEYS.BREASTFEEDING_STRUCTURE,
    queryFn: async () => {
      try {
        // Tentar usar a função otimizada com view materializada primeiro
        const { data, error } = await supabase.rpc('get_breastfeeding_structure_cached');

        if (error) {
          // Fallback para função original se a otimizada falhar
          const { data: fallbackData, error: fallbackError } = await supabase.rpc('get_breastfeeding_structure');
          
          if (fallbackError) {
            throw fallbackError;
          }
          
          return fallbackData as BreastfeedingData;
        }

        return data as BreastfeedingData;
      } catch (err: any) {
        throw err;
      }
    },
    ...CACHE_STRATEGIES.BREASTFEEDING_STRUCTURE,
  });
};

/**
 * Hook para busca otimizada de medicamentos
 * Usa full-text search com ranking
 */
export const useBreastfeedingSearch = (searchQuery: string) => {
  return useQuery({
    queryKey: CACHE_KEYS.BREASTFEEDING_SEARCH(searchQuery),
    queryFn: async () => {
      if (!searchQuery || searchQuery.length < 3) {
        return [];
      }

      try {
        // Usar função de busca otimizada
        const { data, error } = await supabase.rpc('search_breastfeeding_medications_optimized', {
          search_query: searchQuery,
          limit_results: 20
        });

        if (error) {
          // Fallback para busca direta
          const { data: fallbackData, error: fallbackError } = await supabase
            .from('pedbook_breastfeeding_medications')
            .select(`
              id,
              name,
              compatibility_level,
              usage_description,
              additional_info,
              efeitos_no_lactente,
              alternativas_seguras,
              orientacoes_uso,
              section:pedbook_breastfeeding_sections(name),
              subsection:pedbook_breastfeeding_subsections(name)
            `)
            .ilike('name', `%${searchQuery}%`)
            .limit(20);

          if (fallbackError) {
            throw fallbackError;
          }

          return fallbackData || [];
        }

        // Processar dados da função RPC que retorna estrutura aninhada
        const processedData = (data || []).map((item: any) => {
          // A função RPC pode retornar diferentes estruturas:
          // 1. {search_breastfeeding_medications_optimized: {...}}
          // 2. {medication_data: {...}}
          // 3. Dados diretos {...}

          return item.search_breastfeeding_medications_optimized ||
                 item.medication_data ||
                 item;
        });

        return processedData;
      } catch (err: any) {
        throw err;
      }
    },
    enabled: searchQuery.length >= 3,
    ...CACHE_STRATEGIES.BREASTFEEDING_SEARCH,
  });
};

/**
 * Hook para seções (carregamento progressivo)
 */
export const useBreastfeedingSections = () => {
  return useQuery({
    queryKey: CACHE_KEYS.BREASTFEEDING_SECTIONS,
    queryFn: async () => {
      const { data, error } = await supabase
        .from('pedbook_breastfeeding_sections')
        .select('id, name, description, display_order')
        .order('display_order', { nullsLast: true })
        .order('name');

      if (error) throw error;
      return data;
    },
    ...CACHE_STRATEGIES.BREASTFEEDING_SECTIONS,
  });
};

/**
 * Hook para subseções de uma seção específica
 */
export const useBreastfeedingSubsections = (sectionId: string | null) => {
  return useQuery({
    queryKey: CACHE_KEYS.BREASTFEEDING_SUBSECTIONS(sectionId || ''),
    queryFn: async () => {
      if (!sectionId) return [];

      const { data, error } = await supabase
        .from('pedbook_breastfeeding_subsections')
        .select('id, name, description, nesting_level, parent_subsection_id, display_order')
        .eq('section_id', sectionId)
        .order('display_order', { nullsLast: true })
        .order('name');

      if (error) throw error;
      return data;
    },
    enabled: !!sectionId,
    ...CACHE_STRATEGIES.BREASTFEEDING_SUBSECTIONS,
  });
};

/**
 * Hook para medicamentos de uma subseção específica
 */
export const useBreastfeedingMedications = (subsectionId: string | null) => {
  return useQuery({
    queryKey: CACHE_KEYS.BREASTFEEDING_MEDICATIONS(subsectionId || ''),
    queryFn: async () => {
      if (!subsectionId) return [];

      const { data, error } = await supabase
        .from('pedbook_breastfeeding_medications')
        .select(`
          id,
          name,
          compatibility_level,
          usage_description,
          additional_info,
          efeitos_no_lactente,
          alternativas_seguras,
          orientacoes_uso
        `)
        .eq('subsection_id', subsectionId)
        .order('name');

      if (error) throw error;
      return data;
    },
    enabled: !!subsectionId,
    ...CACHE_STRATEGIES.BREASTFEEDING_MEDICATIONS,
  });
};

/**
 * Hook para prefetch inteligente
 */
export const useBreastfeedingPrefetch = () => {
  const queryClient = useQueryClient();

  const prefetchSubsections = (sectionId: string) => {
    queryClient.prefetchQuery({
      queryKey: CACHE_KEYS.BREASTFEEDING_SUBSECTIONS(sectionId),
      queryFn: async () => {
        const { data, error } = await supabase
          .from('pedbook_breastfeeding_subsections')
          .select('id, name, description, nesting_level, parent_subsection_id, display_order')
          .eq('section_id', sectionId)
          .order('display_order', { nullsLast: true })
          .order('name');

        if (error) throw error;
        return data;
      },
      ...CACHE_STRATEGIES.BREASTFEEDING_SUBSECTIONS,
    });
  };

  const prefetchMedications = (subsectionId: string) => {
    queryClient.prefetchQuery({
      queryKey: CACHE_KEYS.BREASTFEEDING_MEDICATIONS(subsectionId),
      queryFn: async () => {
        const { data, error } = await supabase
          .from('pedbook_breastfeeding_medications')
          .select(`
            id,
            name,
            compatibility_level,
            usage_description,
            additional_info,
            efeitos_no_lactente,
            alternativas_seguras,
            orientacoes_uso
          `)
          .eq('subsection_id', subsectionId)
          .order('name');

        if (error) throw error;
        return data;
      },
      ...CACHE_STRATEGIES.BREASTFEEDING_MEDICATIONS,
    });
  };

  return {
    prefetchSubsections,
    prefetchMedications,
  };
};

/**
 * Hook para invalidação de cache
 */
export const useBreastfeedingCacheInvalidation = () => {
  const queryClient = useQueryClient();

  const invalidateAll = () => {
    queryClient.invalidateQueries({ queryKey: ['breastfeeding'] });
  };

  const invalidateStructure = () => {
    queryClient.invalidateQueries({ queryKey: CACHE_KEYS.BREASTFEEDING_STRUCTURE });
  };

  const invalidateSearch = () => {
    queryClient.invalidateQueries({ queryKey: ['breastfeeding-search'] });
  };

  return {
    invalidateAll,
    invalidateStructure,
    invalidateSearch,
  };
};
