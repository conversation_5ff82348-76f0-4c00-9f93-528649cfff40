import { useEffect, useState } from 'react';

interface ChunkVersionCheckerProps {
  checkInterval?: number; // em minutos
  enabled?: boolean;
}

export const ChunkVersionChecker = ({ 
  checkInterval = 30, // 30 minutos por padrão
  enabled = true 
}: ChunkVersionCheckerProps) => {
  const [isCheckingVersion, setIsCheckingVersion] = useState(false);

  useEffect(() => {
    if (!enabled) return;

    let intervalId: NodeJS.Timeout;
    let initialVersion: string | null = null;

    const getCurrentVersion = async (): Promise<string | null> => {
      try {
        // Tentar buscar o index.html para verificar se há uma nova versão
        const response = await fetch('/', { 
          cache: 'no-cache',
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        });
        
        if (!response.ok) return null;
        
        const html = await response.text();
        
        // Extrair hash dos arquivos JS/CSS para detectar mudanças
        const scriptMatches = html.match(/src="[^"]*\.js[^"]*"/g) || [];
        const styleMatches = html.match(/href="[^"]*\.css[^"]*"/g) || [];
        
        const allAssets = [...scriptMatches, ...styleMatches].join('');
        
        // Criar um hash simples baseado nos assets
        let hash = 0;
        for (let i = 0; i < allAssets.length; i++) {
          const char = allAssets.charCodeAt(i);
          hash = ((hash << 5) - hash) + char;
          hash = hash & hash; // Convert to 32bit integer
        }
        
        return hash.toString();
      } catch (error) {
        console.warn('Erro ao verificar versão:', error);
        return null;
      }
    };

    const checkForUpdates = async () => {
      if (isCheckingVersion) return;
      
      setIsCheckingVersion(true);
      
      try {
        const currentVersion = await getCurrentVersion();
        
        if (currentVersion === null) {
          setIsCheckingVersion(false);
          return;
        }
        
        if (initialVersion === null) {
          initialVersion = currentVersion;
          setIsCheckingVersion(false);
          return;
        }
        
        if (currentVersion !== initialVersion) {
          // Mostrar notificação discreta de atualização disponível
          const updateNotification = document.createElement('div');
          updateNotification.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 16px 20px;
            border-radius: 12px;
            font-size: 14px;
            z-index: 10000;
            box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
            max-width: 320px;
            animation: slideInUp 0.4s ease-out;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
          `;
          
          updateNotification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 12px;">
              <div style="
                width: 40px; 
                height: 40px; 
                background: rgba(255, 255, 255, 0.2); 
                border-radius: 50%; 
                display: flex; 
                align-items: center; 
                justify-content: center;
                font-size: 18px;
              ">🚀</div>
              <div style="flex: 1;">
                <div style="font-weight: 600; margin-bottom: 4px;">Nova versão disponível!</div>
                <div style="font-size: 12px; opacity: 0.9;">Atualize para ter acesso às melhorias mais recentes.</div>
              </div>
            </div>
            <div style="margin-top: 12px; display: flex; gap: 8px;">
              <button onclick="window.location.reload()" style="
                background: rgba(255, 255, 255, 0.9);
                color: #1d4ed8;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 12px;
                font-weight: 600;
                flex: 1;
              ">Atualizar Agora</button>
              <button onclick="this.parentElement.parentElement.remove()" style="
                background: rgba(255, 255, 255, 0.1);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.2);
                padding: 8px 12px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 12px;
              ">Depois</button>
            </div>
          `;
          
          // Adicionar animação CSS
          const style = document.createElement('style');
          style.textContent = `
            @keyframes slideInUp {
              from { 
                transform: translateY(100px); 
                opacity: 0; 
              }
              to { 
                transform: translateY(0); 
                opacity: 1; 
              }
            }
          `;
          document.head.appendChild(style);
          document.body.appendChild(updateNotification);
          
          // Auto-remover após 30 segundos se não interagir
          setTimeout(() => {
            if (updateNotification.parentNode) {
              updateNotification.style.animation = 'slideInUp 0.4s ease-out reverse';
              setTimeout(() => {
                updateNotification.remove();
                style.remove();
              }, 400);
            }
          }, 30000);
        }
      } catch (error) {
        console.warn('Erro ao verificar atualizações:', error);
      } finally {
        setIsCheckingVersion(false);
      }
    };

    // Verificação inicial após 5 segundos
    const initialTimeout = setTimeout(() => {
      checkForUpdates();
    }, 5000);

    // Verificações periódicas
    intervalId = setInterval(() => {
      checkForUpdates();
    }, checkInterval * 60 * 1000);

    return () => {
      clearTimeout(initialTimeout);
      clearInterval(intervalId);
    };
  }, [checkInterval, enabled, isCheckingVersion]);

  return null; // Componente invisível
};

export default ChunkVersionChecker;
