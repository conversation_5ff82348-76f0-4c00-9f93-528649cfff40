import { supabase } from '@/integrations/supabase/client';

interface MedicationData {
  id: string;
  name: string;
  slug: string;
  brands?: string;
  description?: string;
  has_bula: boolean;
  has_calculation: boolean;
}

interface RAGEnhancement {
  medications: MedicationData[];
  enhancedResponse: string;
}

/**
 * Serviço RAG para complementar respostas do Dr. Will com dados internos
 */
export class RAGService {
  private static medicationsCache: MedicationData[] = [];
  private static lastCacheUpdate = 0;
  private static CACHE_DURATION = 30 * 60 * 1000; // 30 minutos

  /**
   * Carrega e cacheia dados de medicamentos
   */
  private static async loadMedicationsData(): Promise<MedicationData[]> {
    const now = Date.now();

    // Usar cache se ainda válido
    if (this.medicationsCache.length > 0 && (now - this.lastCacheUpdate) < this.CACHE_DURATION) {
      return this.medicationsCache;
    }

    try {
      const { data, error } = await supabase
        .from('pedbook_medications')
        .select(`
          id,
          name,
          slug,
          brands,
          description,
          pedbook_medication_dosages(id)
        `)
        .order('name');

      if (error) throw error;

      // Processar dados para incluir flags de disponibilidade real
      this.medicationsCache = (data || []).map(med => ({
        ...med,
        has_bula: true, // Por enquanto assumir que todos têm bula
        has_calculation: med.pedbook_medication_dosages && med.pedbook_medication_dosages.length > 0,
      }));

      this.lastCacheUpdate = now;
      return this.medicationsCache;
    } catch (error) {
      console.error('Erro ao carregar medicamentos:', error);
      return [];
    }
  }

  /**
   * Encontra medicamentos mencionados no texto
   */
  private static findMentionedMedications(text: string, medications: MedicationData[]): MedicationData[] {
    const mentioned: MedicationData[] = [];
    const textLower = text.toLowerCase();

    for (const med of medications) {
      let found = false;

      // Verificar nome principal (palavra completa)
      const nameRegex = new RegExp(`\\b${med.name.toLowerCase().replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'i');
      if (nameRegex.test(textLower)) {
        mentioned.push(med);
        found = true;
        continue;
      }

      // Verificar marcas/nomes comerciais (palavra completa)
      if (!found && med.brands) {
        const brands = med.brands.split(',').map(b => b.trim());
        for (const brand of brands) {
          if (brand.length > 2) { // Evitar marcas muito curtas
            const brandRegex = new RegExp(`\\b${brand.toLowerCase().replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'i');
            if (brandRegex.test(textLower)) {
              mentioned.push(med);
              found = true;
              break;
            }
          }
        }
      }
    }

    // Remover duplicatas
    return mentioned.filter((med, index, self) =>
      index === self.findIndex(m => m.id === med.id)
    );
  }

  /**
   * Gera links complementares para medicamentos
   */
  private static generateMedicationLinks(medication: MedicationData): string {
    const links: string[] = [];

    if (medication.has_calculation) {
      links.push(`🧮 [Calcular dosagem](https://pedb.com.br/medicamentos/${medication.slug})`);
    }

    if (medication.has_bula) {
      links.push(`📋 [Bula profissional](https://pedb.com.br/bulas-profissionais/${medication.slug})`);
    }

    return links.join(' • ');
  }

  /**
   * Complementa a resposta do Dr. Will com dados internos
   */
  public static async enhanceResponse(originalResponse: string): Promise<RAGEnhancement> {
    try {
      // Carregar dados de medicamentos
      const medications = await this.loadMedicationsData();
      
      // Encontrar medicamentos mencionados
      const mentionedMedications = this.findMentionedMedications(originalResponse, medications);

      if (mentionedMedications.length === 0) {
        return {
          medications: [],
          enhancedResponse: originalResponse
        };
      }

      // Gerar complemento com links
      let enhancement = '\n\n---\n\n';
      enhancement += '**📚 Recursos da plataforma:**\n\n';

      for (const med of mentionedMedications) {
        const links = this.generateMedicationLinks(med);
        if (links) {
          enhancement += `**${med.name}:** ${links}\n\n`;
        }
      }

      enhancement += '*💡 Links para recursos internos da plataforma PedBook.*';

      return {
        medications: mentionedMedications,
        enhancedResponse: originalResponse + enhancement
      };

    } catch (error) {
      console.error('Erro no RAG Service:', error);
      return {
        medications: [],
        enhancedResponse: originalResponse
      };
    }
  }

  /**
   * Busca medicamentos por termo de pesquisa
   */
  public static async searchMedications(query: string, limit = 5): Promise<MedicationData[]> {
    try {
      const medications = await this.loadMedicationsData();
      const queryLower = query.toLowerCase();

      return medications
        .filter(med => 
          med.name.toLowerCase().includes(queryLower) ||
          (med.brands && med.brands.toLowerCase().includes(queryLower))
        )
        .slice(0, limit);
    } catch (error) {
      console.error('Erro na busca de medicamentos:', error);
      return [];
    }
  }

  /**
   * Limpa o cache (útil para desenvolvimento)
   */
  public static clearCache(): void {
    this.medicationsCache = [];
    this.lastCacheUpdate = 0;
  }
}
