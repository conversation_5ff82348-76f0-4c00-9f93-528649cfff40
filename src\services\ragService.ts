import { supabase } from '@/integrations/supabase/client';

console.log('📦 RAG Service carregado!');  // Debug: verificar se arquivo está sendo importado

/**
 * Calcula similaridade entre duas strings usando Levenshtein distance
 */
function calculateSimilarity(str1: string, str2: string): number {
  const len1 = str1.length;
  const len2 = str2.length;

  if (len1 === 0) return len2;
  if (len2 === 0) return len1;

  const matrix = Array(len2 + 1).fill(null).map(() => Array(len1 + 1).fill(null));

  for (let i = 0; i <= len1; i++) matrix[0][i] = i;
  for (let j = 0; j <= len2; j++) matrix[j][0] = j;

  for (let j = 1; j <= len2; j++) {
    for (let i = 1; i <= len1; i++) {
      const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j - 1][i] + 1,     // deletion
        matrix[j][i - 1] + 1,     // insertion
        matrix[j - 1][i - 1] + cost // substitution
      );
    }
  }

  const maxLen = Math.max(len1, len2);
  return (maxLen - matrix[len2][len1]) / maxLen;
}

interface MedicationData {
  id: string;
  name: string;
  slug: string;
  brands?: string;
  description?: string;
  has_bula: boolean;
  has_calculation: boolean;
}

// Interfaces para futuras expansões
interface ConductData {
  id: string;
  name: string;
  slug: string;
  category: string;
  description?: string;
}

interface ManagementData {
  id: string;
  name: string;
  slug: string;
  category: string;
  description?: string;
}

interface RAGEnhancement {
  medications: MedicationData[];
  enhancedResponse: string;
  showDialog: boolean;
  dialogData?: {
    medications: MedicationData[];
    title: string;
  };
}

/**
 * Mapeamento APENAS de traduções/sinônimos comuns (não dados!)
 * Usado para melhorar detecção quando IA responde em inglês
 */
const COMMON_TRANSLATIONS: Record<string, string[]> = {
  // Apenas traduções mais comuns - dados vêm do banco
  'amoxicillin': ['amoxicilina'],
  'acetaminophen': ['paracetamol'],
  'ibuprofen': ['ibuprofeno'],
  'azithromycin': ['azitromicina'],
  'ceftriaxone': ['ceftriaxona'],
  'cephalexin': ['cefalexina'],
  'clarithromycin': ['claritromicina'],
  'sulfamethoxazole': ['sulfametoxazol'],
  'trimethoprim': ['trimetoprima'],
  'prednisolone': ['prednisolona'],
  'dexamethasone': ['dexametasona'],
  'albuterol': ['salbutamol'],
  'loratadine': ['loratadina'],
  'cetirizine': ['cetirizina'],
  'omeprazole': ['omeprazol'],
  'metronidazole': ['metronidazol'],
  'fluconazole': ['fluconazol'],
  // Reverso (português → inglês) será gerado automaticamente
};

/**
 * Serviço RAG para complementar respostas do Dr. Will com dados internos
 *
 * ESTRATÉGIA ESCALÁVEL:
 * 1. Dados vêm sempre do banco (138 medicamentos + futuras expansões)
 * 2. Traduções/sinônimos apenas para melhorar detecção
 * 3. Fuzzy matching para variações de escrita
 * 4. Facilmente extensível para condutas, manejos, puericultura, etc.
 */
export class RAGService {
  private static medicationsCache: MedicationData[] = [];
  private static lastCacheUpdate = 0;
  private static CACHE_DURATION = 30 * 60 * 1000; // 30 minutos
  private static SIMILARITY_THRESHOLD = 0.8; // 80% de similaridade mínima
  private static translationMap: Map<string, string> = new Map();

  /**
   * Gera mapeamento bidirecional de traduções
   */
  private static generateTranslationMap(): Map<string, string> {
    if (this.translationMap.size > 0) return this.translationMap;

    const map = new Map<string, string>();

    // Inglês → Português
    for (const [english, portuguese] of Object.entries(COMMON_TRANSLATIONS)) {
      for (const ptName of portuguese) {
        map.set(english.toLowerCase(), ptName.toLowerCase());
      }
    }

    // Português → Inglês (reverso)
    for (const [english, portuguese] of Object.entries(COMMON_TRANSLATIONS)) {
      for (const ptName of portuguese) {
        map.set(ptName.toLowerCase(), english.toLowerCase());
      }
    }

    this.translationMap = map;
    return map;
  }

  /**
   * Carrega e cacheia dados de medicamentos
   */
  private static async loadMedicationsData(): Promise<MedicationData[]> {
    const now = Date.now();

    // Usar cache se ainda válido
    if (this.medicationsCache.length > 0 && (now - this.lastCacheUpdate) < this.CACHE_DURATION) {
      console.log(`📋 RAG: Usando cache com ${this.medicationsCache.length} medicamentos`);
      return this.medicationsCache;
    }

    console.log('🔄 RAG: Carregando medicamentos do banco...');
    try {
      const { data, error } = await supabase
        .from('pedbook_medications')
        .select(`
          id,
          name,
          slug,
          brands,
          description
        `)
        .order('name');

      if (error) throw error;

      console.log(`📊 RAG: ${data?.length || 0} medicamentos carregados do banco`);

      // Por enquanto, assumir que todos têm bula e cálculo
      // TODO: Implementar verificação real de dosagens
      this.medicationsCache = (data || []).map(med => ({
        ...med,
        has_bula: true,
        has_calculation: true,
      }));

      this.lastCacheUpdate = now;
      console.log(`✅ RAG: Cache atualizado com ${this.medicationsCache.length} medicamentos`);
      return this.medicationsCache;
    } catch (error) {
      console.error('❌ RAG: Erro ao carregar medicamentos:', error);
      return [];
    }
  }

  /**
   * Encontra medicamentos mencionados no texto com detecção inteligente e fuzzy matching
   */
  private static findMentionedMedications(text: string, medications: MedicationData[]): MedicationData[] {
    const mentioned: MedicationData[] = [];
    const textLower = text.toLowerCase();

    // Extrair palavras do texto (removendo pontuação)
    const words = textLower.match(/\b[a-záàâãéèêíìîóòôõúùûç]+\b/g) || [];

    // Primeiro, verificar padrões compostos específicos
    const compoundMatches = this.findCompoundMedications(text, medications);
    mentioned.push(...compoundMatches);

    // Depois, verificar medicamentos individuais
    for (const med of medications) {
      // Pular se já foi encontrado como composto
      if (mentioned.some(m => m.id === med.id)) continue;

      let found = false;

      // 1. Verificar correspondência exata do nome
      const nameRegex = new RegExp(`\\b${med.name.toLowerCase().replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'i');
      if (nameRegex.test(textLower)) {
        if (!this.isPartOfCompound(med.name, textLower)) {
          mentioned.push(med);
          found = true;
          continue;
        }
      }

      // 2. Verificar traduções usando mapeamento dinâmico
      if (!found) {
        const translationMap = this.generateTranslationMap();

        for (const word of words) {
          if (word.length >= 4) {
            const translation = translationMap.get(word);
            if (translation && translation === med.name.toLowerCase()) {
              mentioned.push(med);
              found = true;
              break;
            }
          }
        }
      }

      // 3. Fuzzy matching para palavras similares
      if (!found) {
        const translationMap = this.generateTranslationMap();

        for (const word of words) {
          if (word.length >= 4) { // Só palavras com 4+ caracteres
            // Similaridade com nome do medicamento
            const similarity = calculateSimilarity(word, med.name.toLowerCase());
            if (similarity >= this.SIMILARITY_THRESHOLD) {
              mentioned.push(med);
              found = true;
              break;
            }

            // Similaridade com traduções conhecidas
            for (const [key, value] of translationMap.entries()) {
              if (value === med.name.toLowerCase()) {
                const translationSimilarity = calculateSimilarity(word, key);
                if (translationSimilarity >= this.SIMILARITY_THRESHOLD) {
                  mentioned.push(med);
                  found = true;
                  break;
                }
              }
            }
            if (found) break;
          }
        }
      }

      // 4. Verificar marcas/nomes comerciais
      if (!found && med.brands) {
        const brands = med.brands.split(',').map(b => b.trim());
        for (const brand of brands) {
          if (brand.length > 2) {
            const brandRegex = new RegExp(`\\b${brand.toLowerCase().replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'i');
            if (brandRegex.test(textLower) && !this.isPartOfCompound(brand, textLower)) {
              mentioned.push(med);
              found = true;
              break;
            }
          }
        }
      }
    }

    // Remover duplicatas
    return mentioned.filter((med, index, self) =>
      index === self.findIndex(m => m.id === med.id)
    );
  }

  /**
   * Busca medicamentos compostos no texto
   */
  private static findCompoundMedications(text: string, medications: MedicationData[]): MedicationData[] {
    const found: MedicationData[] = [];
    const textLower = text.toLowerCase();

    // Padrões compostos mais abrangentes (incluindo inglês)
    const compoundPatterns = [
      // Amoxicilina + Clavulanato
      /amoxicilina?\s*(?:com|with|\+)\s*clavulanato?/gi,
      /amoxicillin?\s*(?:com|with|\+)\s*clavulanic?\s*acid?/gi,
      /amoxicillin?\s*clavulanate?/gi,

      // Sulfametoxazol + Trimetoprima
      /sulfametoxazol?\s*(?:com|with|\+)\s*trimetoprima?/gi,
      /sulfamethoxazole?\s*(?:com|with|\+)\s*trimethoprim?/gi,
      /bactrim/gi,
      /septra/gi,
    ];

    for (const pattern of compoundPatterns) {
      const matches = text.match(pattern);
      if (matches) {
        for (const match of matches) {
          const compoundMed = this.findSpecificCompound(match, medications);
          if (compoundMed && !found.some(m => m.id === compoundMed.id)) {
            found.push(compoundMed);
          }
        }
      }
    }

    return found;
  }

  /**
   * Busca medicamento composto específico
   */
  private static findSpecificCompound(compound: string, medications: MedicationData[]): MedicationData | null {
    const compoundLower = compound.toLowerCase();

    // Mapeamento direto para slugs conhecidos
    if (compoundLower.includes('amoxicil') && (compoundLower.includes('clavul') || compoundLower.includes('clavul'))) {
      return medications.find(med => med.slug === 'amoxicilina-clavulanato') || null;
    }

    if (compoundLower.includes('sulfamet') && compoundLower.includes('trimet')) {
      return medications.find(med => med.slug === 'sulfametoxazol-trimetoprima') || null;
    }

    if (compoundLower.includes('bactrim') || compoundLower.includes('septra')) {
      return medications.find(med => med.slug === 'sulfametoxazol-trimetoprima') || null;
    }

    return null;
  }

  /**
   * Verifica se um medicamento é parte de um composto mencionado
   */
  private static isPartOfCompound(medicationName: string, text: string): boolean {
    const name = medicationName.toLowerCase();

    // Padrões que indicam que é parte de um composto
    const compoundIndicators = [
      `${name}\\s+com\\s+`,
      `${name}\\s*\\+\\s*`,
      `\\s+com\\s+${name}`,
      `\\s*\\+\\s*${name}`,
    ];

    return compoundIndicators.some(pattern =>
      new RegExp(pattern, 'i').test(text)
    );
  }

  /**
   * Gera links complementares para medicamentos
   */
  private static generateMedicationLinks(medication: MedicationData): string {
    const links: string[] = [];

    if (medication.has_calculation) {
      links.push(`🧮 [Calcular dosagem](https://pedb.com.br/medicamentos/${medication.slug})`);
    }

    if (medication.has_bula) {
      links.push(`📋 [Bula profissional](https://pedb.com.br/bulas-profissionais/${medication.slug})`);
    }

    return links.join(' • ');
  }

  /**
   * Complementa a resposta do Dr. Will com dados internos
   */
  public static async enhanceResponse(originalResponse: string): Promise<RAGEnhancement> {
    console.log('🚀 RAG: enhanceResponse chamado!', originalResponse.substring(0, 100));

    try {
      // Carregar dados de medicamentos
      const medications = await this.loadMedicationsData();

      // Debug: Log do texto sendo analisado
      console.log('🔍 RAG: Analisando resposta:', originalResponse.substring(0, 200) + '...');

      // Teste simples: verificar se contém "amoxicilina"
      const hasAmoxicilina = originalResponse.toLowerCase().includes('amoxicilina');
      console.log('🧪 RAG: Teste simples - contém "amoxicilina"?', hasAmoxicilina);

      // Encontrar medicamentos mencionados
      const mentionedMedications = this.findMentionedMedications(originalResponse, medications);

      // Debug: Log dos medicamentos encontrados
      console.log('💊 RAG: Medicamentos detectados:', mentionedMedications.map(m => ({
        name: m.name,
        slug: m.slug,
        has_calculation: m.has_calculation,
        has_bula: m.has_bula
      })));

      if (mentionedMedications.length === 0) {
        // Teste: forçar detecção se contém "amoxicilina"
        if (hasAmoxicilina && medications.length > 0) {
          const amoxicilina = medications.find(m => m.name.toLowerCase().includes('amoxicilina'));
          if (amoxicilina) {
            console.log('🔧 RAG: Forçando detecção de amoxicilina para teste');
            const testResult = {
              medications: [amoxicilina],
              enhancedResponse: originalResponse + '\n\n---\n\n**📚 Recursos da plataforma:**\n\n**Amoxicilina:** 🧮 [Calcular dosagem](https://pedb.com.br/medicamentos/amoxicilina) • 📋 [Bula profissional](https://pedb.com.br/bulas-profissionais/amoxicilina)\n\n*💡 Links para recursos internos da plataforma PedBook.*',
              showDialog: false
            };
            return testResult;
          }
        }

        return {
          medications: [],
          enhancedResponse: originalResponse,
          showDialog: false
        };
      }

      // Decidir se usar dialog ou complemento inline
      if (mentionedMedications.length > 2) {
        // Muitos medicamentos: usar dialog
        return {
          medications: mentionedMedications,
          enhancedResponse: originalResponse + '\n\n*💊 Encontrei recursos da plataforma para os medicamentos mencionados.*',
          showDialog: true,
          dialogData: {
            medications: mentionedMedications,
            title: `Recursos para ${mentionedMedications.length} medicamentos`
          }
        };
      } else {
        // Poucos medicamentos: complemento inline
        let enhancement = '\n\n---\n\n';
        enhancement += '**📚 Recursos da plataforma:**\n\n';

        for (const med of mentionedMedications) {
          const links = this.generateMedicationLinks(med);
          if (links) {
            enhancement += `**${med.name}:** ${links}\n\n`;
          }
        }

        enhancement += '*💡 Links para recursos internos da plataforma PedBook.*';

        return {
          medications: mentionedMedications,
          enhancedResponse: originalResponse + enhancement,
          showDialog: false
        };
      }

    } catch (error) {
      console.error('Erro no RAG Service:', error);
      return {
        medications: [],
        enhancedResponse: originalResponse
      };
    }
  }

  /**
   * Busca medicamentos por termo de pesquisa
   */
  public static async searchMedications(query: string, limit = 5): Promise<MedicationData[]> {
    try {
      const medications = await this.loadMedicationsData();
      const queryLower = query.toLowerCase();

      return medications
        .filter(med => 
          med.name.toLowerCase().includes(queryLower) ||
          (med.brands && med.brands.toLowerCase().includes(queryLower))
        )
        .slice(0, limit);
    } catch (error) {
      console.error('Erro na busca de medicamentos:', error);
      return [];
    }
  }

  /**
   * Limpa o cache (útil para desenvolvimento)
   */
  public static clearCache(): void {
    this.medicationsCache = [];
    this.lastCacheUpdate = 0;
  }
}
