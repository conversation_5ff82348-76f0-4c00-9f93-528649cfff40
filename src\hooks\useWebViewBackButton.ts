import { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

/**
 * Hook específico para gerenciar o botão voltar em WebView Android
 * Implementa múltiplas estratégias para interceptar o botão físico
 */
export const useWebViewBackButton = () => {
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // Detectar WebView Android com mais precisão
    const detectWebView = () => {
      const ua = navigator.userAgent.toLowerCase();
      const isAndroid = /android/i.test(ua);
      const isWebView = /wv/.test(ua) || /webview/.test(ua);
      const hasAndroidInterface = 'Android' in window || 'AndroidInterface' in window;
      const isChrome = /chrome/.test(ua) && !/edg/.test(ua);
      const isSamsung = /samsungbrowser/.test(ua);
      
      return isAndroid && (isWebView || hasAndroidInterface || (!isChrome && !isSamsung));
    };

    if (!detectWebView()) {
      return; // Não é WebView, não fazer nada
    }

    console.log('🔧 Configurando interceptação avançada do botão voltar para WebView');

    // Estratégia 1: Manipulação agressiva do histórico
    const setupHistoryManipulation = () => {
      // Adicionar múltiplas entradas no histórico para "capturar" o botão voltar
      const addHistoryEntries = () => {
        for (let i = 0; i < 3; i++) {
          window.history.pushState(
            { webview: true, index: i }, 
            '', 
            location.pathname + (i > 0 ? `#webview-${i}` : '')
          );
        }
      };

      addHistoryEntries();

      // Interceptar mudanças no histórico
      const handlePopState = (event: PopStateEvent) => {
        event.preventDefault();
        
        // Verificar se é uma navegação do WebView
        if (event.state && event.state.webview) {
          // Navegar internamente
          handleInternalNavigation();
          // Recriar entradas do histórico
          setTimeout(addHistoryEntries, 100);
        }
      };

      window.addEventListener('popstate', handlePopState);
      return () => window.removeEventListener('popstate', handlePopState);
    };

    // Estratégia 2: Interceptação de eventos de teclado
    const setupKeyboardInterception = () => {
      const handleKeyDown = (event: KeyboardEvent) => {
        // Interceptar teclas que podem ser mapeadas para o botão voltar
        if (event.key === 'Escape' || 
            event.key === 'Backspace' || 
            event.keyCode === 27 || 
            event.keyCode === 8) {
          
          // Verificar se não está em um input
          const target = event.target as HTMLElement;
          if (target.tagName !== 'INPUT' && target.tagName !== 'TEXTAREA') {
            event.preventDefault();
            event.stopPropagation();
            handleInternalNavigation();
          }
        }
      };

      document.addEventListener('keydown', handleKeyDown, true);
      return () => document.removeEventListener('keydown', handleKeyDown, true);
    };

    // Estratégia 3: Interface JavaScript para Android
    const setupAndroidInterface = () => {
      // Criar interface global para o Android chamar
      (window as any).webViewBackHandler = {
        onBackPressed: () => {
          console.log('📱 Botão voltar interceptado via interface Android');
          return handleInternalNavigation();
        },
        canGoBack: () => {
          return location.pathname !== '/';
        },
        getCurrentPath: () => {
          return location.pathname;
        }
      };

      // Interface alternativa
      (window as any).handleBackButton = () => {
        return handleInternalNavigation();
      };

      return () => {
        delete (window as any).webViewBackHandler;
        delete (window as any).handleBackButton;
      };
    };

    // Estratégia 4: Escutar eventos customizados do script WebView
    const setupCustomEventListeners = () => {
      const handleWebViewBackButton = (event: CustomEvent) => {
        console.log('📱 Evento customizado de botão voltar recebido:', event.detail);
        handleInternalNavigation();
      };

      const handleWebViewReady = (event: CustomEvent) => {
        console.log('✅ WebView script pronto:', event.detail);
      };

      document.addEventListener('webview-back-button', handleWebViewBackButton as EventListener);
      document.addEventListener('webview-ready', handleWebViewReady as EventListener);

      return () => {
        document.removeEventListener('webview-back-button', handleWebViewBackButton as EventListener);
        document.removeEventListener('webview-ready', handleWebViewReady as EventListener);
      };
    };

    // Estratégia 5: Monitoramento de visibilidade
    const setupVisibilityMonitoring = () => {
      let isVisible = true;

      const handleVisibilityChange = () => {
        if (document.hidden && isVisible) {
          // App foi minimizado, pode ser devido ao botão voltar
          isVisible = false;
          console.log('📱 App minimizado - possível botão voltar');
        } else if (!document.hidden && !isVisible) {
          // App voltou a ficar visível
          isVisible = true;
          console.log('📱 App restaurado');
        }
      };

      document.addEventListener('visibilitychange', handleVisibilityChange);
      return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
    };

    // Função para navegar internamente
    const handleInternalNavigation = (): boolean => {
      console.log(`🔄 Navegação interna solicitada de: ${location.pathname}`);

      // Se estiver na home, permitir sair
      if (location.pathname === '/') {
        console.log('🏠 Na home - permitindo sair do app');
        return false;
      }

      // Rotas principais
      const mainRoutes = [
        "/medicamentos", "/medicamentos/painel", "/puericultura",
        "/dr-will", "/calculadoras", "/flowcharts", "/condutas",
        "/bulas", "/interacoes", "/cid", "/plataformadeestudos",
        "/feedback", "/perfil", "/busca", "/configuracoes", "/newsletters", "/pedidrop"
      ];

      // Se está em rota principal, ir para home
      if (mainRoutes.includes(location.pathname)) {
        console.log('📍 Rota principal - navegando para home');
        navigate("/");
        return true;
      }

      // Se está em subrota, ir para rota pai
      for (const route of mainRoutes) {
        if (location.pathname.startsWith(route + "/")) {
          console.log(`📍 Subrota de ${route} - navegando para rota pai`);
          navigate(route);
          return true;
        }
      }

      // Fallback: voltar uma página
      console.log('🔙 Fallback - navegando para trás');
      navigate(-1);
      return true;
    };

    // Aplicar todas as estratégias
    const cleanupFunctions = [
      setupHistoryManipulation(),
      setupKeyboardInterception(),
      setupAndroidInterface(),
      setupCustomEventListeners(),
      setupVisibilityMonitoring()
    ];

    // Notificar que está pronto
    console.log('✅ WebView back button handler configurado');

    // Cleanup
    return () => {
      cleanupFunctions.forEach(cleanup => cleanup && cleanup());
      console.log('🧹 WebView back button handler removido');
    };

  }, [location.pathname, navigate]);
};
