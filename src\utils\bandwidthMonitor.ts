/**
 * Sistema de Monitoramento de Bandwidth e Alertas
 * Previne consumo excessivo e detecta loops infinitos
 */

interface BandwidthMetrics {
  requestsPerMinute: number;
  totalRequests: number;
  averageResponseSize: number;
  totalBandwidth: number;
  timestamp: number;
}

interface AlertConfig {
  maxRequestsPerMinute: number;
  maxBandwidthPerHour: number; // MB
  alertCallback?: (alert: BandwidthAlert) => void;
}

interface BandwidthAlert {
  type: 'WARNING' | 'CRITICAL';
  message: string;
  metrics: BandwidthMetrics;
  timestamp: number;
}

class BandwidthMonitor {
  private metrics: BandwidthMetrics[] = [];
  private alerts: BandwidthAlert[] = [];
  private config: AlertConfig;
  private isMonitoring: boolean = false;

  constructor(config: AlertConfig) {
    this.config = config;
  }

  // Iniciar monitoramento
  startMonitoring(): void {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    console.log('📊 [BandwidthMonitor] Monitoramento iniciado');

    // Interceptar fetch para monitorar requests
    this.interceptFetch();
    
    // Verificar métricas a cada minuto
    setInterval(() => {
      this.checkMetrics();
    }, 60000);

    // Limpar dados antigos a cada hora
    setInterval(() => {
      this.cleanOldData();
    }, 3600000);
  }

  // Parar monitoramento
  stopMonitoring(): void {
    this.isMonitoring = false;
    console.log('📊 [BandwidthMonitor] Monitoramento parado');
  }

  // Interceptar fetch para coletar métricas
  private interceptFetch(): void {
    const originalFetch = window.fetch;
    
    window.fetch = async (...args) => {
      const startTime = Date.now();
      
      try {
        const response = await originalFetch(...args);
        const endTime = Date.now();
        
        // Calcular tamanho da resposta (estimativa)
        const contentLength = response.headers.get('content-length');
        const responseSize = contentLength ? parseInt(contentLength) : 0;
        
        // Registrar métrica
        this.recordRequest(responseSize, endTime - startTime);
        
        return response;
      } catch (error) {
        // Registrar erro
        this.recordRequest(0, Date.now() - startTime);
        throw error;
      }
    };
  }

  // Registrar request
  private recordRequest(responseSize: number, duration: number): void {
    const now = Date.now();
    const currentMinute = Math.floor(now / 60000);
    
    // Encontrar ou criar métrica para o minuto atual
    let currentMetric = this.metrics.find(m => 
      Math.floor(m.timestamp / 60000) === currentMinute
    );
    
    if (!currentMetric) {
      currentMetric = {
        requestsPerMinute: 0,
        totalRequests: 0,
        averageResponseSize: 0,
        totalBandwidth: 0,
        timestamp: now
      };
      this.metrics.push(currentMetric);
    }
    
    // Atualizar métricas
    currentMetric.requestsPerMinute++;
    currentMetric.totalRequests++;
    currentMetric.totalBandwidth += responseSize;
    currentMetric.averageResponseSize = currentMetric.totalBandwidth / currentMetric.totalRequests;
  }

  // Verificar métricas e gerar alertas
  private checkMetrics(): void {
    const now = Date.now();
    const lastMinute = this.metrics[this.metrics.length - 1];
    
    if (!lastMinute) return;

    // Verificar requests por minuto
    if (lastMinute.requestsPerMinute > this.config.maxRequestsPerMinute) {
      this.generateAlert('CRITICAL', 
        `Requests excessivos: ${lastMinute.requestsPerMinute}/min (limite: ${this.config.maxRequestsPerMinute}/min)`,
        lastMinute
      );
    }

    // Verificar bandwidth por hora
    const lastHourMetrics = this.metrics.filter(m => 
      now - m.timestamp < 3600000 // última hora
    );
    
    const hourlyBandwidth = lastHourMetrics.reduce((total, m) => 
      total + m.totalBandwidth, 0
    ) / (1024 * 1024); // Converter para MB
    
    if (hourlyBandwidth > this.config.maxBandwidthPerHour) {
      this.generateAlert('CRITICAL',
        `Bandwidth excessivo: ${hourlyBandwidth.toFixed(2)}MB/h (limite: ${this.config.maxBandwidthPerHour}MB/h)`,
        lastMinute
      );
    }

    // Detectar possível loop infinito
    const last5Minutes = this.metrics.slice(-5);
    if (last5Minutes.length === 5) {
      const avgRequests = last5Minutes.reduce((sum, m) => sum + m.requestsPerMinute, 0) / 5;
      
      if (avgRequests > this.config.maxRequestsPerMinute * 0.8) {
        this.generateAlert('WARNING',
          `Possível loop detectado: ${avgRequests.toFixed(1)} requests/min consistentes`,
          lastMinute
        );
      }
    }
  }

  // Gerar alerta
  private generateAlert(type: 'WARNING' | 'CRITICAL', message: string, metrics: BandwidthMetrics): void {
    const alert: BandwidthAlert = {
      type,
      message,
      metrics,
      timestamp: Date.now()
    };

    this.alerts.push(alert);

    // Log do alerta
    const logFn = type === 'CRITICAL' ? console.error : console.warn;
    logFn(`🚨 [BandwidthMonitor] ${type}: ${message}`);

    // Log no sistema de problemas para debug
    import('./problemLogger').then(({ logBandwidthAlert }) => {
      logBandwidthAlert(
        type,
        metrics.requestsPerMinute,
        this.config.maxRequestsPerMinute,
        {
          message,
          totalBandwidth: metrics.totalBandwidth,
          averageResponseSize: metrics.averageResponseSize,
          totalRequests: metrics.totalRequests,
          timestamp: metrics.timestamp
        }
      );
    });

    // Callback personalizado
    if (this.config.alertCallback) {
      this.config.alertCallback(alert);
    }

    // Limitar número de alertas armazenados
    if (this.alerts.length > 100) {
      this.alerts = this.alerts.slice(-50);
    }
  }

  // Limpar dados antigos
  private cleanOldData(): void {
    const now = Date.now();
    const sixHoursAgo = now - (6 * 3600000);
    
    // Manter apenas últimas 6 horas de métricas
    this.metrics = this.metrics.filter(m => m.timestamp > sixHoursAgo);
    
    // Manter apenas últimas 24h de alertas
    const oneDayAgo = now - (24 * 3600000);
    this.alerts = this.alerts.filter(a => a.timestamp > oneDayAgo);
  }

  // Obter métricas atuais
  getMetrics(): {
    current: BandwidthMetrics | null;
    hourly: BandwidthMetrics[];
    alerts: BandwidthAlert[];
  } {
    const now = Date.now();
    const lastHour = this.metrics.filter(m => now - m.timestamp < 3600000);
    
    return {
      current: this.metrics[this.metrics.length - 1] || null,
      hourly: lastHour,
      alerts: this.alerts.slice(-10) // Últimos 10 alertas
    };
  }

  // Resetar métricas
  reset(): void {
    this.metrics = [];
    this.alerts = [];
    console.log('📊 [BandwidthMonitor] Métricas resetadas');
  }
}

// Instância global do monitor
export const bandwidthMonitor = new BandwidthMonitor({
  maxRequestsPerMinute: 100, // Limite conservador
  maxBandwidthPerHour: 50, // 50MB por hora
  alertCallback: (alert) => {
    // Callback para alertas críticos
    if (alert.type === 'CRITICAL') {
      // Aqui poderia enviar para sistema de alertas externo
      console.error('🚨 ALERTA CRÍTICO DE BANDWIDTH:', alert);
    }
  }
});

// Hook para usar o monitor em componentes
export const useBandwidthMonitor = () => {
  const startMonitoring = () => bandwidthMonitor.startMonitoring();
  const stopMonitoring = () => bandwidthMonitor.stopMonitoring();
  const getMetrics = () => bandwidthMonitor.getMetrics();
  const reset = () => bandwidthMonitor.reset();

  return {
    startMonitoring,
    stopMonitoring,
    getMetrics,
    reset
  };
};

// Função para inicializar monitoramento automático
export const initBandwidthMonitoring = () => {
  // Iniciar apenas em produção
  if (process.env.NODE_ENV === 'production') {
    bandwidthMonitor.startMonitoring();
    console.log('📊 [BandwidthMonitor] Monitoramento automático iniciado');
  }
};
