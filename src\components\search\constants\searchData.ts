// 🚀 OTIMIZAÇÃO: Dados estáticos para busca - evita recriação a cada query

export const CALCULATORS = [
  { id: 'apgar', name: 'Calculadora de Apgar', path: '/calculadoras/apgar' },
  { id: 'rodwell', name: '<PERSON><PERSON><PERSON><PERSON> <PERSON>', path: '/calculadoras/rodwell' },
  { id: 'capurro', name: 'Calculador<PERSON> de Capurro', path: '/calculadoras/capurro' },
  { id: 'capurro-neuro', name: 'Calculadora de Capurro Neurológico', path: '/calculadoras/capurro-neuro' },
  { id: 'finnegan', name: '<PERSON><PERSON><PERSON><PERSON> <PERSON>', path: '/calculadoras/finnegan' },
  { id: 'gina', name: 'Calculadora GINA', path: '/calculadoras/gina' },
  { id: 'glasgow', name: 'Calculadora de Glasgow', path: '/calculadoras/glasgow' },
  { id: 'bmi', name: '<PERSON><PERSON><PERSON><PERSON> de IMC', path: '/calculadoras/imc' },
] as const;

export const FLOWCHARTS = [
  { id: 'dengue', name: 'Fluxograma de Dengue', path: '/flowcharts/dengue', description: 'Manejo de casos suspeitos de dengue' },
  { id: 'asthma', name: 'Fluxograma de Crise Asmática', path: '/flowcharts/asthma', description: 'Manejo de crise asmática em pediatria' },
  { id: 'dka', name: 'Fluxograma de Cetoacidose Diabética', path: '/flowcharts/dka', description: 'Manejo de cetoacidose diabética em pediatria' },
  { id: 'anaphylaxis', name: 'Fluxograma de Anafilaxia', path: '/flowcharts/anaphylaxis', description: 'Manejo de anafilaxia em pediatria' },
  { id: 'seizure', name: 'Fluxograma de Crise Convulsiva', path: '/flowcharts/seizure', description: 'Manejo de crise convulsiva em pediatria' },
  { id: 'pecarn', name: 'Fluxograma PECARN - Trauma Craniano', path: '/flowcharts/pecarn', description: 'Avaliação de trauma craniano em pediatria' },
  { id: 'hydration', name: 'Hidratação Venosa de Manutenção', path: '/calculadoras/hidratacao', description: 'Cálculo da hidratação venosa (Holliday-Segar)' },
  // Fluxogramas de animais peçonhentos com nomes comuns
  { id: 'scorpion', name: 'Fluxograma de Acidente Escorpiônico (Escorpião Amarelo)', path: '/flowcharts/venomous/scorpion', description: 'Manejo de acidentes com escorpião' },
  { id: 'bothropic', name: 'Fluxograma de Acidente Botrópico (Jararaca)', path: '/flowcharts/venomous/bothropic', description: 'Manejo de acidentes com jararaca' },
  { id: 'crotalic', name: 'Fluxograma de Acidente Crotálico (Cascavel)', path: '/flowcharts/venomous/crotalic', description: 'Manejo de acidentes com cascavel' },
  { id: 'elapidic', name: 'Fluxograma de Acidente Elapídico (Coral Verdadeira)', path: '/flowcharts/venomous/elapidic', description: 'Manejo de acidentes com coral verdadeira' },
  { id: 'phoneutria', name: 'Fluxograma de Acidente Fonêutrico (Aranha Armadeira)', path: '/flowcharts/venomous/phoneutria', description: 'Manejo de acidentes com aranha armadeira' },
  { id: 'loxoscelic', name: 'Fluxograma de Acidente Loxoscélico (Aranha Marrom)', path: '/flowcharts/venomous/loxoscelic', description: 'Manejo de acidentes com aranha marrom' }
] as const;

export const CHILDCARE_ITEMS = [
  { id: 'growth', name: 'Curvas de Crescimento', path: '/puericultura/curva-de-crescimento' },
  { id: 'vaccines', name: 'Vacinas', path: '/puericultura/calendario-vacinal' },
  { id: 'formulas', name: 'Fórmulas Infantis', path: '/puericultura/formulas' },
  { id: 'supplementation', name: 'Suplementação', path: '/puericultura/suplementacao-infantil' },
] as const;

// Função utilitária para normalizar texto (reutilizada)
export const normalizeText = (text: string): string => {
  return text
    .toLowerCase()
    .normalize("NFD")
    .replace(/[\u0300-\u036f]/g, "");
};

// Função otimizada para filtrar dados estáticos
export const filterStaticData = <T extends { name: string; description?: string }>(
  data: readonly T[],
  searchTerm: string,
  normalizedTerm: string,
  maxResults: number = 10
): (T & { type: string })[] => {
  return data
    .filter(item => {
      const itemNameNormalized = normalizeText(item.name);
      const itemDescNormalized = item.description ? normalizeText(item.description) : '';
      
      return (
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        itemNameNormalized.includes(normalizedTerm) ||
        (item.description && (
          item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
          itemDescNormalized.includes(normalizedTerm)
        ))
      );
    })
    .slice(0, maxResults) as (T & { type: string })[];
};

// Tipos para TypeScript
export type Calculator = typeof CALCULATORS[number];
export type Flowchart = typeof FLOWCHARTS[number];
export type ChildcareItem = typeof CHILDCARE_ITEMS[number];
