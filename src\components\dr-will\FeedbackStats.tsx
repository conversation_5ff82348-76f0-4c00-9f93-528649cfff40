import React, { useEffect } from 'react';
import { BarChart3, ThumbsUp, ThumbsDown, TrendingUp } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useFeedback } from '@/hooks/useFeedback';

export const FeedbackStats: React.FC = () => {
  const { feedbackStats, loadStats, getFeedbackHistory } = useFeedback();

  useEffect(() => {
    loadStats();
  }, [loadStats]);

  const history = getFeedbackHistory();
  const recentFeedbacks = history.slice(-10).reverse(); // Últimos 10 feedbacks

  if (feedbackStats.totalFeedbacks === 0) {
    return (
      <Card className="w-full max-w-md">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <BarChart3 className="h-5 w-5" />
            Feedback do Dr. Will
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-gray-500 dark:text-gray-400 py-4">
            <p className="text-sm">Nenhum feedback ainda</p>
            <p className="text-xs mt-1">As avaliações aparecerão aqui</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <BarChart3 className="h-5 w-5" />
          Feedback do Dr. Will
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Estatísticas principais */}
        <div className="grid grid-cols-3 gap-3">
          <div className="text-center">
            <div className="flex items-center justify-center w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-full mx-auto mb-1">
              <ThumbsUp className="h-4 w-4 text-green-600 dark:text-green-400" />
            </div>
            <div className="text-lg font-semibold text-green-600 dark:text-green-400">
              {feedbackStats.positiveFeedbacks}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">Positivos</div>
          </div>

          <div className="text-center">
            <div className="flex items-center justify-center w-8 h-8 bg-red-100 dark:bg-red-900/30 rounded-full mx-auto mb-1">
              <ThumbsDown className="h-4 w-4 text-red-600 dark:text-red-400" />
            </div>
            <div className="text-lg font-semibold text-red-600 dark:text-red-400">
              {feedbackStats.negativeFeedbacks}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">Negativos</div>
          </div>

          <div className="text-center">
            <div className="flex items-center justify-center w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full mx-auto mb-1">
              <TrendingUp className="h-4 w-4 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="text-lg font-semibold text-blue-600 dark:text-blue-400">
              {feedbackStats.satisfactionRate.toFixed(0)}%
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">Satisfação</div>
          </div>
        </div>

        {/* Barra de progresso */}
        <div className="space-y-2">
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>Taxa de satisfação</span>
            <span>{feedbackStats.totalFeedbacks} avaliações</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full transition-all duration-500"
              style={{ width: `${feedbackStats.satisfactionRate}%` }}
            />
          </div>
        </div>

        {/* Feedbacks recentes */}
        {recentFeedbacks.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Feedbacks recentes:
            </h4>
            <div className="space-y-1 max-h-32 overflow-y-auto">
              {recentFeedbacks.map((feedback, index) => (
                <div
                  key={index}
                  className="flex items-start gap-2 p-2 bg-gray-50 dark:bg-slate-800/50 rounded text-xs"
                >
                  {feedback.type === 'positive' ? (
                    <ThumbsUp className="h-3 w-3 text-green-500 mt-0.5 flex-shrink-0" />
                  ) : (
                    <ThumbsDown className="h-3 w-3 text-red-500 mt-0.5 flex-shrink-0" />
                  )}
                  <div className="flex-1 min-w-0">
                    {feedback.comment ? (
                      <p className="text-gray-600 dark:text-gray-300 line-clamp-2">
                        "{feedback.comment}"
                      </p>
                    ) : (
                      <p className="text-gray-500 dark:text-gray-400 italic">
                        {feedback.type === 'positive' ? 'Feedback positivo' : 'Feedback negativo'}
                      </p>
                    )}
                    <p className="text-gray-400 dark:text-gray-500 mt-1">
                      {new Date(feedback.timestamp).toLocaleDateString('pt-BR')}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
