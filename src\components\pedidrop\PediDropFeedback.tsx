import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useUser } from "@supabase/auth-helpers-react";
import {
  Star,
  MessageSquare,
  ThumbsUp,
  ThumbsDown,
  Clock,
  Target,
  Lightbulb,
  AlertTriangle,
  Send,
  Heart,
  Droplets,
  TrendingUp,
  TrendingDown,
  Meh
} from "lucide-react";

interface PediDropFeedbackProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  postId?: string;
  postTitle?: string;
}

type FeedbackType = 'continue' | 'improve' | 'discontinue';
type RatingValue = 1 | 2 | 3 | 4 | 5;

const FEEDBACK_OPTIONS = [
  {
    type: 'continue' as FeedbackType,
    icon: TrendingUp,
    title: 'Continue assim!',
    description: 'O PediDrop está ótimo, continue com este formato',
    color: 'bg-green-500',
    bgColor: 'bg-green-50 dark:bg-green-900/20',
    borderColor: 'border-green-200 dark:border-green-800',
    textColor: 'text-green-700 dark:text-green-300'
  },
  {
    type: 'improve' as FeedbackType,
    icon: Meh,
    title: 'Pode melhorar',
    description: 'Gosto do formato, mas tem pontos a melhorar',
    color: 'bg-yellow-500',
    bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
    borderColor: 'border-yellow-200 dark:border-yellow-800',
    textColor: 'text-yellow-700 dark:text-yellow-300'
  },
  {
    type: 'discontinue' as FeedbackType,
    icon: TrendingDown,
    title: 'Repensar formato',
    description: 'Não está funcionando, precisa de mudanças grandes',
    color: 'bg-red-500',
    bgColor: 'bg-red-50 dark:bg-red-900/20',
    borderColor: 'border-red-200 dark:border-red-800',
    textColor: 'text-red-700 dark:text-red-300'
  }
];

export const PediDropFeedback: React.FC<PediDropFeedbackProps> = ({
  open,
  onOpenChange,
  postId,
  postTitle
}) => {
  const [selectedFeedback, setSelectedFeedback] = useState<FeedbackType | null>(null);
  const [rating, setRating] = useState<RatingValue>(5);
  const [comment, setComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hasSubmitted, setHasSubmitted] = useState(false);
  const { toast } = useToast();
  const user = useUser();

  // Verificar se já enviou feedback para este post
  useEffect(() => {
    const checkExistingFeedback = async () => {
      if (!user || !postId) return;

      try {
        const { data } = await supabase
          .from('pedbook_feedbacks')
          .select('id')
          .eq('post_id', postId)
          .eq('user_id', user.id)
          .eq('feedback_category', 'pedidrop')
          .maybeSingle();

        if (data) {
          setHasSubmitted(true);
        }
      } catch (error) {
        console.error('Erro ao verificar feedback existente:', error);
      }
    };

    if (open) {
      checkExistingFeedback();
    }
  }, [user, postId, open]);

  const handleSubmit = async () => {
    if (!user) {
      toast({
        title: "Login necessário",
        description: "Você precisa estar logado para enviar feedback.",
        variant: "destructive",
      });
      return;
    }

    if (!selectedFeedback) {
      toast({
        title: "Selecione uma opção",
        description: "Por favor, escolha uma das opções de feedback.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Buscar o ID do tipo de feedback PediDrop
      const { data: feedbackType } = await supabase
        .from('pedbook_feedback_types')
        .select('id')
        .eq('name', 'PediDrop')
        .single();

      if (!feedbackType) {
        throw new Error('Tipo de feedback PediDrop não encontrado');
      }

      const { error } = await supabase
        .from('pedbook_feedbacks')
        .insert({
          type_id: feedbackType.id,
          user_id: user.id,
          title: `Feedback PediDrop - ${selectedFeedback}`,
          message: comment.trim() || `Avaliação do formato PediDrop: ${selectedFeedback} (${rating} estrelas)`,
          feedback_category: 'pedidrop',
          post_id: postId || 'general',
          feedback_type: selectedFeedback,
          rating: rating,
        });

      if (error) {
        if (error.code === '23505') {
          toast({
            title: "Feedback já enviado",
            description: "Você já avaliou este PediDrop anteriormente.",
            variant: "destructive",
          });
          setHasSubmitted(true);
          return;
        }
        throw error;
      }

      toast({
        title: "Feedback enviado!",
        description: "Obrigado por nos ajudar a melhorar o PediDrop! 🙏",
      });

      setHasSubmitted(true);
      
      // Fechar dialog após 2 segundos
      setTimeout(() => {
        onOpenChange(false);
      }, 2000);

    } catch (error: any) {
      toast({
        title: "Erro ao enviar feedback",
        description: error.message || "Tente novamente mais tarde.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onOpenChange(false);
      // Reset form
      setTimeout(() => {
        setSelectedFeedback(null);
        setRating(5);
        setComment('');
        setHasSubmitted(false);
      }, 300);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90dvh] overflow-y-auto">
        <DialogHeader className="space-y-3">
          <div className="flex items-center justify-center gap-2">
            <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg">
              <Droplets className="h-6 w-6 text-white" />
            </div>
            <DialogTitle className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text">
              Feedback do PediDrop
            </DialogTitle>
          </div>
          
          <DialogDescription className="text-center text-base leading-relaxed">
            <strong>Precisamos da sua opinião!</strong> O PediDrop é um formato novo e queremos saber se está sendo relevante para você. 
            Sua avaliação nos ajuda a decidir se continuamos, melhoramos ou repensamos este modelo.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {hasSubmitted ? (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="text-center py-8 space-y-4"
            >
              <div className="mx-auto w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                <Heart className="h-8 w-8 text-green-600 dark:text-green-400" />
              </div>
              <h3 className="text-lg font-semibold text-green-700 dark:text-green-300">
                Obrigado pelo seu feedback!
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Você já avaliou este PediDrop. Sua opinião é muito importante para nós! 🙏
              </p>
            </motion.div>
          ) : (
            <>
              {/* Opções de Feedback */}
              <div className="space-y-3">
                <h3 className="font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                  <Target className="h-5 w-5 text-blue-600" />
                  Como você avalia o formato PediDrop?
                </h3>
                
                <div className="grid gap-3">
                  {FEEDBACK_OPTIONS.map((option) => {
                    const Icon = option.icon;
                    const isSelected = selectedFeedback === option.type;
                    
                    return (
                      <motion.button
                        key={option.type}
                        onClick={() => setSelectedFeedback(option.type)}
                        className={`p-4 rounded-lg border-2 transition-all duration-200 text-left ${
                          isSelected
                            ? `${option.bgColor} ${option.borderColor} ${option.textColor}`
                            : 'bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                        }`}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className="flex items-start gap-3">
                          <div className={`p-2 rounded-lg ${option.color}`}>
                            <Icon className="h-5 w-5 text-white" />
                          </div>
                          <div className="flex-1">
                            <h4 className="font-semibold">{option.title}</h4>
                            <p className="text-sm opacity-80">{option.description}</p>
                          </div>
                          {isSelected && (
                            <div className="text-green-600 dark:text-green-400">
                              <ThumbsUp className="h-5 w-5" />
                            </div>
                          )}
                        </div>
                      </motion.button>
                    );
                  })}
                </div>
              </div>

              {/* Rating */}
              <div className="space-y-3">
                <h3 className="font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                  <Star className="h-5 w-5 text-yellow-500" />
                  Nota geral (1-5 estrelas)
                </h3>
                
                <div className="flex justify-center gap-2">
                  {[1, 2, 3, 4, 5].map((value) => (
                    <button
                      key={value}
                      onClick={() => setRating(value as RatingValue)}
                      className={`transition-all duration-200 hover:scale-110 p-2 ${
                        rating >= value 
                          ? 'text-yellow-400 dark:text-yellow-300' 
                          : 'text-gray-300 dark:text-gray-600'
                      }`}
                      type="button"
                    >
                      <Star 
                        className="w-8 h-8" 
                        fill={rating >= value ? 'currentColor' : 'none'}
                        strokeWidth={1.5}
                      />
                    </button>
                  ))}
                </div>
              </div>

              {/* Comentário */}
              <div className="space-y-3">
                <h3 className="font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                  <MessageSquare className="h-5 w-5 text-purple-600" />
                  Comentários e sugestões (opcional)
                </h3>
                
                <Textarea
                  value={comment}
                  onChange={(e) => setComment(e.target.value)}
                  placeholder="Conte-nos o que pensa sobre o PediDrop: o que está funcionando bem? O que poderia melhorar? Que tipo de conteúdo gostaria de ver? Sua opinião é fundamental para decidirmos o futuro deste formato!"
                  className="min-h-[100px] resize-none"
                  maxLength={1000}
                />
                
                <div className="text-right text-sm text-gray-500">
                  {comment.length}/1000 caracteres
                </div>
              </div>

              {/* Botões */}
              <div className="flex gap-3 pt-4">
                <Button
                  variant="outline"
                  onClick={handleClose}
                  disabled={isSubmitting}
                  className="flex-1"
                >
                  Talvez depois
                </Button>
                
                <Button
                  onClick={handleSubmit}
                  disabled={isSubmitting || !selectedFeedback}
                  className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      Enviando...
                    </>
                  ) : (
                    <>
                      <Send className="h-4 w-4 mr-2" />
                      Enviar Feedback
                    </>
                  )}
                </Button>
              </div>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
