import { useEffect } from 'react';

interface ChunkErrorHandlerOptions {
  maxRetries?: number;
  retryDelay?: number;
  showUserMessage?: boolean;
}

export const useChunkErrorHandler = (options: ChunkErrorHandlerOptions = {}) => {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    showUserMessage = true
  } = options;

  useEffect(() => {
    let retryCount = 0;
    let isReloading = false;

    const handleChunkError = (error: ErrorEvent) => {
      const errorMessage = error.message?.toLowerCase() || '';
      const isChunkError = 
        errorMessage.includes('loading chunk') ||
        errorMessage.includes('failed to fetch dynamically imported module') ||
        errorMessage.includes('mime type') ||
        errorMessage.includes('module script') ||
        error.filename?.includes('.js');

      if (isChunkError && !isReloading) {
        console.warn('🔄 Chunk loading error detected:', error.message);
        
        if (retryCount < maxRetries) {
          retryCount++;
          console.log(`🔄 Tentativa ${retryCount}/${maxRetries} de recarregamento...`);
          
          if (showUserMessage && retryCount === 1) {
            // Mostrar mensagem discreta para o usuário
            const notification = document.createElement('div');
            notification.style.cssText = `
              position: fixed;
              top: 20px;
              right: 20px;
              background: #3b82f6;
              color: white;
              padding: 12px 16px;
              border-radius: 8px;
              font-size: 14px;
              z-index: 10000;
              box-shadow: 0 4px 12px rgba(0,0,0,0.15);
              max-width: 300px;
              animation: slideIn 0.3s ease-out;
            `;
            notification.innerHTML = `
              <div style="display: flex; align-items: center; gap: 8px;">
                <div style="width: 16px; height: 16px; border: 2px solid white; border-top: 2px solid transparent; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                <span>Atualizando recursos...</span>
              </div>
            `;
            
            // Adicionar animações CSS
            const style = document.createElement('style');
            style.textContent = `
              @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
              }
              @keyframes spin {
                to { transform: rotate(360deg); }
              }
            `;
            document.head.appendChild(style);
            document.body.appendChild(notification);
            
            // Remover notificação após 3 segundos
            setTimeout(() => {
              if (notification.parentNode) {
                notification.style.animation = 'slideIn 0.3s ease-out reverse';
                setTimeout(() => {
                  notification.remove();
                  style.remove();
                }, 300);
              }
            }, 3000);
          }
          
          // Tentar recarregar após delay
          setTimeout(() => {
            if (!isReloading) {
              isReloading = true;
              window.location.reload();
            }
          }, retryDelay * retryCount);
        } else {
          console.error('❌ Máximo de tentativas de recarregamento atingido');
          
          if (showUserMessage) {
            // Mostrar mensagem de erro persistente
            const errorNotification = document.createElement('div');
            errorNotification.style.cssText = `
              position: fixed;
              top: 20px;
              right: 20px;
              background: #ef4444;
              color: white;
              padding: 16px;
              border-radius: 8px;
              font-size: 14px;
              z-index: 10000;
              box-shadow: 0 4px 12px rgba(0,0,0,0.15);
              max-width: 350px;
              animation: slideIn 0.3s ease-out;
            `;
            errorNotification.innerHTML = `
              <div>
                <div style="font-weight: 600; margin-bottom: 8px;">Erro de Carregamento</div>
                <div style="margin-bottom: 12px;">Alguns recursos não puderam ser carregados. Recarregue a página manualmente.</div>
                <button onclick="window.location.reload()" style="
                  background: rgba(255,255,255,0.2);
                  border: 1px solid rgba(255,255,255,0.3);
                  color: white;
                  padding: 6px 12px;
                  border-radius: 4px;
                  cursor: pointer;
                  font-size: 12px;
                ">Recarregar Página</button>
              </div>
            `;
            document.body.appendChild(errorNotification);
          }
        }
      }
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      const reason = event.reason?.message?.toLowerCase() || '';
      const isChunkError = 
        reason.includes('loading chunk') ||
        reason.includes('failed to fetch dynamically imported module') ||
        reason.includes('mime type') ||
        reason.includes('module script');

      if (isChunkError && !isReloading) {
        console.warn('🔄 Chunk loading promise rejection detected:', event.reason);
        handleChunkError({
          message: event.reason?.message || 'Chunk loading error',
          filename: '',
        } as ErrorEvent);
      }
    };

    // Adicionar listeners
    window.addEventListener('error', handleChunkError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    // Cleanup
    return () => {
      window.removeEventListener('error', handleChunkError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, [maxRetries, retryDelay, showUserMessage]);
};

// Hook específico para páginas críticas
export const useChunkErrorHandlerCritical = () => {
  return useChunkErrorHandler({
    maxRetries: 1,
    retryDelay: 500,
    showUserMessage: true
  });
};

// Hook para páginas menos críticas
export const useChunkErrorHandlerSilent = () => {
  return useChunkErrorHandler({
    maxRetries: 2,
    retryDelay: 2000,
    showUserMessage: false
  });
};
