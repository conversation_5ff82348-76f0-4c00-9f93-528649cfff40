import React from "react";
import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent } from "@/components/ui/card";

interface LoadingStatesProps {
  type: 'structure' | 'sections' | 'subsections' | 'medications' | 'search';
  count?: number;
}

interface ProgressIndicatorProps {
  message: string;
  progress?: number;
  showSpinner?: boolean;
}

/**
 * Componente de indicador de progresso
 */
export const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({ 
  message, 
  progress, 
  showSpinner = true 
}) => (
  <div className="flex items-center justify-center gap-3 py-8">
    {showSpinner && (
      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-pink-500"></div>
    )}
    <div className="text-center">
      <span className="text-lg text-gray-600 dark:text-gray-400">{message}</span>
      {progress !== undefined && (
        <div className="mt-2 w-48 bg-gray-200 rounded-full h-2 dark:bg-gray-700">
          <div 
            className="bg-pink-500 h-2 rounded-full transition-all duration-300" 
            style={{ width: `${progress}%` }}
          ></div>
        </div>
      )}
    </div>
  </div>
);

/**
 * Skeleton para estrutura completa (carregamento inicial)
 */
export const StructureSkeleton: React.FC = () => (
  <div className="space-y-6">
    <ProgressIndicator message="Carregando medicamentos para amamentação..." />
    
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {Array.from({ length: 6 }).map((_, i) => (
        <Card key={i} className="animate-pulse">
          <CardContent className="p-4">
            <div className="flex items-start gap-3 mb-3">
              <Skeleton className="h-10 w-10 rounded-lg" />
              <div className="flex-1">
                <Skeleton className="h-5 w-3/4 mb-2" />
                <Skeleton className="h-4 w-1/2" />
              </div>
            </div>
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-4 w-2/3" />
          </CardContent>
        </Card>
      ))}
    </div>
    
    <div className="text-center">
      <div className="inline-flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
        <div className="animate-pulse h-2 w-2 bg-pink-500 rounded-full"></div>
        <span>Organizando categorias e medicamentos...</span>
      </div>
    </div>
  </div>
);

/**
 * Skeleton para seções
 */
export const SectionsSkeleton: React.FC<{ count?: number }> = ({ count = 6 }) => (
  <div className="space-y-4">
    <ProgressIndicator message="Carregando categorias..." showSpinner={true} />
    
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {Array.from({ length: count }).map((_, i) => (
        <Card key={i} className="animate-pulse border-2">
          <CardContent className="p-4">
            <div className="flex items-center gap-3 mb-3">
              <Skeleton className="h-8 w-8 rounded-lg" />
              <div className="flex-1">
                <Skeleton className="h-5 w-3/4 mb-1" />
                <Skeleton className="h-3 w-1/2" />
              </div>
              <Skeleton className="h-4 w-4" />
            </div>
            <Skeleton className="h-3 w-full mb-2" />
            <Skeleton className="h-3 w-4/5" />
          </CardContent>
        </Card>
      ))}
    </div>
  </div>
);

/**
 * Skeleton para subseções
 */
export const SubsectionsSkeleton: React.FC<{ count?: number }> = ({ count = 4 }) => (
  <div className="space-y-4">
    <ProgressIndicator message="Carregando subcategorias..." />
    
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {Array.from({ length: count }).map((_, i) => (
        <Card key={i} className="animate-pulse border-2 border-blue-200 dark:border-blue-700">
          <CardContent className="p-4">
            <div className="flex items-center gap-3 mb-3">
              <Skeleton className="h-8 w-8 rounded-lg bg-blue-200 dark:bg-blue-800" />
              <div className="flex-1">
                <Skeleton className="h-4 w-2/3 mb-1" />
                <Skeleton className="h-3 w-1/3" />
              </div>
              <Skeleton className="h-4 w-4" />
            </div>
            <Skeleton className="h-3 w-full" />
          </CardContent>
        </Card>
      ))}
    </div>
  </div>
);

/**
 * Skeleton para medicamentos
 */
export const MedicationsSkeleton: React.FC<{ count?: number }> = ({ count = 6 }) => (
  <div className="space-y-4">
    <ProgressIndicator message="Carregando medicamentos..." />
    
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {Array.from({ length: count }).map((_, i) => (
        <Card key={i} className="animate-pulse border-2">
          <CardContent className="p-4">
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center gap-3 flex-1">
                <Skeleton className="h-8 w-8 rounded-full" />
                <div className="flex-1">
                  <Skeleton className="h-5 w-3/4 mb-2" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              </div>
              <div className="text-right">
                <Skeleton className="h-6 w-16 mb-1" />
                <Skeleton className="h-3 w-12" />
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4 mb-3">
              <div>
                <Skeleton className="h-3 w-16 mb-1" />
                <Skeleton className="h-3 w-full" />
              </div>
              <div>
                <Skeleton className="h-3 w-12 mb-1" />
                <Skeleton className="h-3 w-3/4" />
              </div>
            </div>
            
            <Skeleton className="h-8 w-full rounded" />
          </CardContent>
        </Card>
      ))}
    </div>
  </div>
);

/**
 * Skeleton para resultados de busca
 */
export const SearchSkeleton: React.FC<{ count?: number }> = ({ count = 3 }) => (
  <div className="space-y-4">
    <div className="flex items-center justify-center gap-3 py-6">
      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
      <span className="text-blue-600 dark:text-blue-400">Pesquisando medicamentos...</span>
    </div>
    
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {Array.from({ length: count }).map((_, i) => (
        <Card key={i} className="animate-pulse border-blue-200 dark:border-blue-700">
          <CardContent className="p-4">
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center gap-3 flex-1">
                <Skeleton className="h-8 w-8 rounded-full bg-blue-200 dark:bg-blue-800" />
                <div className="flex-1">
                  <Skeleton className="h-5 w-3/4 mb-2" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              </div>
              <Skeleton className="h-6 w-16" />
            </div>
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-4 w-2/3" />
          </CardContent>
        </Card>
      ))}
    </div>
  </div>
);

/**
 * Componente principal que escolhe o skeleton apropriado
 */
export const BreastfeedingSkeletons: React.FC<LoadingStatesProps> = ({ type, count }) => {
  switch (type) {
    case 'structure':
      return <StructureSkeleton />;
    case 'sections':
      return <SectionsSkeleton count={count} />;
    case 'subsections':
      return <SubsectionsSkeleton count={count} />;
    case 'medications':
      return <MedicationsSkeleton count={count} />;
    case 'search':
      return <SearchSkeleton count={count} />;
    default:
      return <StructureSkeleton />;
  }
};

/**
 * Mensagens de loading específicas
 */
export const LoadingMessages = {
  STRUCTURE: "Carregando medicamentos para amamentação...",
  SECTIONS: "Carregando categorias...",
  SUBSECTIONS: "Carregando subcategorias...",
  MEDICATIONS: "Carregando medicamentos...",
  SEARCH: "Pesquisando medicamentos...",
  ORGANIZING: "Organizando dados...",
  OPTIMIZING: "Otimizando carregamento...",
} as const;
