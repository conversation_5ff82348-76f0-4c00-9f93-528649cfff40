import HelmetWrapper from "@/components/utils/HelmetWrapper";

interface CalculatorSEOProps {
  title: string;
  description: string;
  slug: string;
  category: 'neonatal' | 'pediatric' | 'assessment' | 'respiratory' | 'neurological' | 'nutritional';
  keywords: string[];
  clinicalUse: string;
  targetAge: string;
  parameters: string[];
  interpretation: string[];
  clinicalSignificance: string;
  relatedConditions: string[];
}

export const CalculatorSEO = ({
  title,
  description,
  slug,
  category,
  keywords,
  clinicalUse,
  targetAge,
  parameters,
  interpretation,
  clinicalSignificance,
  relatedConditions
}: CalculatorSEOProps) => {

  // Gerar título dinâmico otimizado
  const generateTitle = () => {
    let seoTitle = `${title}`;
    
    if (category === 'neonatal') {
      seoTitle += ` - Calculadora Neonatal`;
    } else if (category === 'pediatric') {
      seoTitle += ` - Calculadora Pediátrica`;
    } else if (category === 'assessment') {
      seoTitle += ` - Escala de Avaliação`;
    } else if (category === 'respiratory') {
      seoTitle += ` - Avaliação Respiratória`;
    } else if (category === 'neurological') {
      seoTitle += ` - Avaliação Neurológica`;
    } else if (category === 'nutritional') {
      seoTitle += ` - Avaliação Nutricional`;
    }
    
    seoTitle += ` | PedBook`;
    
    return seoTitle.substring(0, 60);
  };

  // Gerar descrição dinâmica
  const generateDescription = () => {
    let desc = `${title}: ${description}`;
    
    if (targetAge) {
      desc += ` Aplicável em ${targetAge}.`;
    }
    
    desc += ` ${clinicalUse}`;
    
    if (parameters.length > 0) {
      desc += ` Avalia: ${parameters.slice(0, 3).join(', ')}.`;
    }
    
    desc += ` Ferramenta essencial para pediatras e neonatologistas.`;
    
    return desc.substring(0, 160);
  };

  // Gerar keywords dinâmicas
  const generateKeywords = () => {
    const baseKeywords = [
      `calculadora ${title.toLowerCase()}`,
      `${title.toLowerCase()} pediatria`,
      `${title.toLowerCase()} neonatologia`,
      `escala ${title.toLowerCase()}`,
      `score ${title.toLowerCase()}`,
      `avaliação ${title.toLowerCase()}`
    ];

    // Adicionar keywords por categoria
    if (category === 'neonatal') {
      baseKeywords.push(
        `${title.toLowerCase()} recém-nascido`,
        `${title.toLowerCase()} neonatal`,
        `calculadora neonatal`,
        `avaliação neonatal`
      );
    }

    if (category === 'pediatric') {
      baseKeywords.push(
        `${title.toLowerCase()} criança`,
        `${title.toLowerCase()} pediátrico`,
        `calculadora pediátrica`,
        `avaliação pediátrica`
      );
    }

    if (category === 'assessment') {
      baseKeywords.push(
        `escala avaliação`,
        `score clínico`,
        `avaliação clínica`,
        `protocolo avaliação`
      );
    }

    // Adicionar keywords específicas passadas
    baseKeywords.push(...keywords);

    // Adicionar parâmetros como keywords
    parameters.forEach(param => {
      baseKeywords.push(`${param.toLowerCase()} ${title.toLowerCase()}`);
    });

    // Adicionar condições relacionadas
    relatedConditions.forEach(condition => {
      baseKeywords.push(`${condition.toLowerCase()} ${title.toLowerCase()}`);
    });

    return baseKeywords.join(", ");
  };

  // URL canônica
  const canonicalUrl = `https://pedb.com.br/calculadoras/${slug}`;

  // Schema.org para aplicação médica
  const softwareApplicationSchema = {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": `${title} - Calculadora Médica`,
    "description": generateDescription(),
    "url": canonicalUrl,
    "applicationCategory": "MedicalApplication",
    "applicationSubCategory": "MedicalCalculator",
    "operatingSystem": "Web Browser",

    "featureList": [
      `Cálculo de ${title}`,
      "Interface intuitiva",
      "Resultados instantâneos",
      "Interpretação clínica",
      "Referências científicas"
    ],
    "audience": {
      "@type": "MedicalAudience",
      "audienceType": "Médicos pediatras e neonatologistas"
    },
    "about": {
      "@type": "MedicalProcedure",
      "name": title,
      "description": clinicalUse
    },
    "publisher": {
      "@type": "Organization",
      "name": "PedBook",
      "url": "https://pedb.com.br",
      "logo": {
        "@type": "ImageObject",
        "url": "https://pedb.com.br/faviconx.webp"
      }
    }
  };

  // Schema.org para página médica
  const medicalPageSchema = {
    "@context": "https://schema.org",
    "@type": "MedicalWebPage",
    "name": generateTitle(),
    "description": generateDescription(),
    "url": canonicalUrl,
    "mainContentOfPage": {
      "@type": "WebPageElement",
      "cssSelector": "main"
    },
    "specialty": category === 'neonatal' ? "Neonatologia" : "Pediatria",
    "audience": {
      "@type": "MedicalAudience",
      "audienceType": "Médicos pediatras e neonatologistas"
    },
    "about": {
      "@type": "MedicalTest",
      "name": title,
      "usedToDiagnose": relatedConditions.join(', ')
    },
    "lastReviewed": new Date().toISOString().split('T')[0],
    "reviewedBy": {
      "@type": "Organization",
      "name": "PedBook",
      "url": "https://pedb.com.br"
    }
  };

  // Breadcrumb Schema
  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "PedBook",
        "item": "https://pedb.com.br"
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Calculadoras",
        "item": "https://pedb.com.br/calculadoras"
      },
      {
        "@type": "ListItem",
        "position": 3,
        "name": title,
        "item": canonicalUrl
      }
    ]
  };

  return (
    <HelmetWrapper>
      {/* Título e Descrição Dinâmicos */}
      <title>{generateTitle()}</title>
      <meta name="description" content={generateDescription()} />
      <meta name="keywords" content={generateKeywords()} />

      {/* Meta tags médicas específicas */}
      <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1" />
      <meta name="googlebot" content="index, follow" />
      <meta name="medical-content" content="medical-calculator" />
      <meta name="target-audience" content="healthcare-professionals" />
      <meta name="content-type" content="medical-tool" />
      <meta name="clinical-specialty" content={category === 'neonatal' ? 'neonatology' : 'pediatrics'} />
      <meta name="calculator-type" content={category} />
      
      {/* Geo targeting */}
      <meta name="geo.region" content="BR" />
      <meta name="geo.country" content="Brazil" />
      <meta name="language" content="Portuguese" />

      {/* Open Graph */}
      <meta property="og:title" content={generateTitle()} />
      <meta property="og:description" content={generateDescription()} />
      <meta property="og:type" content="website" />
      <meta property="og:url" content={canonicalUrl} />
      <meta property="og:image" content="https://pedb.com.br/faviconx.webp" />
      <meta property="og:image:alt" content={`${title} - PedBook`} />
      <meta property="og:site_name" content="PedBook" />
      <meta property="og:locale" content="pt_BR" />
      <meta property="article:section" content="Medicina" />
      <meta property="article:tag" content="Calculadoras" />
      <meta property="article:tag" content="Pediatria" />
      <meta property="article:tag" content={category} />
      {relatedConditions.map((condition, index) => (
        <meta key={index} property="article:tag" content={condition} />
      ))}

      {/* Twitter Cards */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={generateTitle()} />
      <meta name="twitter:description" content={generateDescription()} />
      <meta name="twitter:image" content="https://pedb.com.br/faviconx.webp" />
      <meta name="twitter:site" content="@pedbook" />

      {/* Canonical */}
      <link rel="canonical" href={canonicalUrl} />

      {/* Schema.org - Aplicação Médica */}
      <script type="application/ld+json">
        {JSON.stringify(softwareApplicationSchema)}
      </script>

      {/* Schema.org - Página Médica */}
      <script type="application/ld+json">
        {JSON.stringify(medicalPageSchema)}
      </script>

      {/* Schema.org - Breadcrumb */}
      <script type="application/ld+json">
        {JSON.stringify(breadcrumbSchema)}
      </script>
    </HelmetWrapper>
  );
};
