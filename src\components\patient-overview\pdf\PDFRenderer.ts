import jsPD<PERSON> from 'jspdf';
import { PDF_CONFIG } from './PDFConfig';
import { PDFUtils } from './PDFUtils';
import { PatientData, AnalysisData, SupplementationData, VaccineData } from './DataGenerators';

/**
 * Classe responsável por renderizar seções do PDF
 */
export class PDFRenderer {
  private pdf: jsPDF;
  private currentY: number;
  private logoBase64: string;

  constructor(pdf: jsPDF, logoBase64: string = '') {
    this.pdf = pdf;
    this.currentY = PDF_CONFIG.page.margin;
    this.logoBase64 = logoBase64;
  }

  /**
   * Verifica se precisa quebrar página
   */
  private checkPageBreak(neededSpace: number): boolean {
    if (this.currentY + neededSpace > PDF_CONFIG.page.height - PDF_CONFIG.page.margin) {
      this.pdf.addPage();
      this.addWatermark();
      this.currentY = PDF_CONFIG.page.margin;
      return true;
    }
    return false;
  }

  /**
   * Adiciona marca d'água
   */
  private addWatermark(): void {
    if (this.logoBase64) {
      this.pdf.saveGraphicsState();
      this.pdf.setGState(new this.pdf.GState({ opacity: PDF_CONFIG.logo.opacity }));

      const logoX = (PDF_CONFIG.page.width - PDF_CONFIG.logo.size) / 2;
      const logoY = (PDF_CONFIG.page.height - PDF_CONFIG.logo.size) / 2 + PDF_CONFIG.logo.offsetY;

      try {
        this.pdf.addImage(this.logoBase64, 'PNG', logoX, logoY, PDF_CONFIG.logo.size, PDF_CONFIG.logo.size, undefined, 'NONE');
      } catch (error) {
        try {
          this.pdf.addImage(this.logoBase64, 'PNG', logoX, logoY, PDF_CONFIG.logo.size, PDF_CONFIG.logo.size);
        } catch (fallbackError) {
          // Falha silenciosa ao adicionar logo
        }
      }

      this.pdf.restoreGraphicsState();
    }
  }

  /**
   * Adiciona título de seção
   */
  private addSectionTitle(title: string, icon: string = ''): void {
    this.checkPageBreak(15);
    this.pdf.setFontSize(PDF_CONFIG.fonts.section);
    this.pdf.setFont('helvetica', 'bold');
    this.pdf.setTextColor(...PDF_CONFIG.colors.primary);

    const cleanTitle = PDFUtils.cleanText(title);
    const cleanIcon = PDFUtils.cleanText(icon);
    const iconText = cleanIcon ? `${cleanIcon} ` : '';
    
    this.pdf.text(`${iconText}${cleanTitle}`, PDF_CONFIG.page.margin, this.currentY);
    this.currentY += 8;

    // Linha separadora
    this.pdf.setDrawColor(...PDF_CONFIG.colors.primary);
    this.pdf.setLineWidth(0.5);
    this.pdf.line(PDF_CONFIG.page.margin, this.currentY, PDF_CONFIG.page.margin + PDF_CONFIG.page.width - (PDF_CONFIG.page.margin * 2), this.currentY);
    this.currentY += 8;
  }

  /**
   * Adiciona texto normal
   */
  private addText(label: string, value: string, x: number = PDF_CONFIG.page.margin): void {
    this.checkPageBreak(PDF_CONFIG.page.lineHeight);
    this.pdf.setFontSize(PDF_CONFIG.fonts.normal);
    this.pdf.setFont('helvetica', 'bold');
    this.pdf.setTextColor(...PDF_CONFIG.colors.text);

    const cleanLabel = PDFUtils.cleanText(label);
    const cleanValue = PDFUtils.cleanText(value);

    this.pdf.text(`${cleanLabel}: `, x, this.currentY);

    this.pdf.setFont('helvetica', 'normal');
    const labelWidth = this.pdf.getTextWidth(`${cleanLabel}: `);
    this.pdf.text(cleanValue, x + labelWidth, this.currentY);
    this.currentY += PDF_CONFIG.page.lineHeight;
  }

  /**
   * Adiciona card colorido
   */
  private addColoredCard(title: string, content: string[], bgColor: [number, number, number]): void {
    this.checkPageBreak(20);

    const cardHeight = 6 + (content.length * PDF_CONFIG.page.lineHeight);
    const contentWidth = PDF_CONFIG.page.width - (PDF_CONFIG.page.margin * 2);

    // Fundo colorido
    this.pdf.setFillColor(...bgColor);
    this.pdf.rect(PDF_CONFIG.page.margin, this.currentY - 2, contentWidth, cardHeight, 'F');

    // Título do card
    this.pdf.setFontSize(11);
    this.pdf.setFont('helvetica', 'bold');
    this.pdf.setTextColor(...PDF_CONFIG.colors.text);

    const cleanTitle = PDFUtils.cleanText(title);
    this.pdf.text(cleanTitle, PDF_CONFIG.page.margin + 3, this.currentY + 3);
    this.currentY += 8;

    // Conteúdo do card
    this.pdf.setFontSize(PDF_CONFIG.fonts.small);
    this.pdf.setFont('helvetica', 'normal');
    content.forEach(line => {
      const cleanLine = PDFUtils.cleanText(line);
      const lines = this.pdf.splitTextToSize(cleanLine, contentWidth - 6);
      lines.forEach((splitLine: string) => {
        this.pdf.text(splitLine, PDF_CONFIG.page.margin + 3, this.currentY);
        this.currentY += PDF_CONFIG.page.lineHeight - 1;
      });
    });

    this.currentY += 4;
  }

  /**
   * Renderiza cabeçalho
   */
  renderHeader(): void {
    this.addWatermark();

    // Título principal
    this.pdf.setFontSize(PDF_CONFIG.fonts.title);
    this.pdf.setFont('helvetica', 'bold');
    this.pdf.setTextColor(...PDF_CONFIG.colors.primary);
    const title = PDFUtils.cleanText(PDF_CONFIG.texts.title);
    const titleWidth = this.pdf.getTextWidth(title);
    this.pdf.text(title, (PDF_CONFIG.page.width - titleWidth) / 2, this.currentY);
    this.currentY += 8;

    // Subtítulo
    this.pdf.setFontSize(11);
    this.pdf.setFont('helvetica', 'normal');
    this.pdf.setTextColor(...PDF_CONFIG.colors.gray);
    const subtitle = PDFUtils.cleanText(PDF_CONFIG.texts.subtitle);
    const subtitleWidth = this.pdf.getTextWidth(subtitle);
    this.pdf.text(subtitle, (PDF_CONFIG.page.width - subtitleWidth) / 2, this.currentY);
    this.currentY += 6;

    // Data e hora
    const dateTime = PDFUtils.formatDateTime();
    const dateWidth = this.pdf.getTextWidth(dateTime);
    this.pdf.text(dateTime, (PDF_CONFIG.page.width - dateWidth) / 2, this.currentY);
    this.currentY += 15;

    // Linha separadora principal
    this.pdf.setDrawColor(...PDF_CONFIG.colors.primary);
    this.pdf.setLineWidth(1);
    this.pdf.line(PDF_CONFIG.page.margin, this.currentY, PDF_CONFIG.page.margin + PDF_CONFIG.page.width - (PDF_CONFIG.page.margin * 2), this.currentY);
    this.currentY += 15;
  }

  /**
   * Renderiza dados do paciente
   */
  renderPatientData(patientData: PatientData, useCorrectedAge: boolean): void {
    this.addSectionTitle(PDF_CONFIG.texts.sections.patient, PDF_CONFIG.texts.icons.patient);

    // Layout em duas colunas
    const col1X = PDF_CONFIG.page.margin;
    const col2X = PDF_CONFIG.page.margin + (PDF_CONFIG.page.width - (PDF_CONFIG.page.margin * 2)) / 2;
    const startY = this.currentY;

    // Coluna 1
    this.currentY = startY;
    this.addText('Nome', patientData.name || 'Nao informado', col1X);
    this.addText('Idade', PDFUtils.formatAge(patientData.age, useCorrectedAge), col1X);
    this.addText('Genero', patientData.gender === 'male' ? 'Masculino' : 'Feminino', col1X);
    this.addText('Tipo de Idade', useCorrectedAge ? 'Idade Corrigida' : 'Idade Cronologica', col1X);

    // Coluna 2
    const col2Y = startY;
    this.currentY = col2Y;
    this.addText('Peso Atual', PDFUtils.formatWeight(patientData.weight), col2X);
    this.addText('Altura', PDFUtils.formatHeight(patientData.height), col2X);
    this.addText('PC', PDFUtils.formatHeight(patientData.headCircumference), col2X);
    this.addText('Maturidade', PDFUtils.getMaturidade(patientData.gestationalAge), col2X);

    this.currentY = Math.max(this.currentY, startY + (4 * PDF_CONFIG.page.lineHeight)) + 10;
  }

  /**
   * Renderiza dados perinatais
   */
  renderPerinatalData(patientData: PatientData, analysisData: AnalysisData): void {
    this.addSectionTitle(PDF_CONFIG.texts.sections.perinatal, PDF_CONFIG.texts.icons.perinatal);

    const col1X = PDF_CONFIG.page.margin;
    const col2X = PDF_CONFIG.page.margin + (PDF_CONFIG.page.width - (PDF_CONFIG.page.margin * 2)) / 2;
    const startY = this.currentY;

    this.currentY = startY;
    this.addText('Peso ao Nascer', `${patientData.birthWeight}g`, col1X);
    this.addText('Idade Gestacional', PDFUtils.formatGestationalAge(patientData.gestationalAge), col1X);

    this.currentY = startY;
    this.addText('AME', patientData.exclusiveBreastfeeding ? 'Sim' : 'Nao', col2X);
    this.addText('Classificacao', `${analysisData.weightAnalysis.classification} (P${analysisData.weightAnalysis.percentile})`, col2X);

    this.currentY = Math.max(this.currentY, startY + (2 * PDF_CONFIG.page.lineHeight)) + 10;
  }

  /**
   * Renderiza análise antropométrica
   */
  renderAnalysis(analysisData: AnalysisData, patientData: PatientData): void {
    this.addSectionTitle(PDF_CONFIG.texts.sections.analysis, PDF_CONFIG.texts.icons.analysis);

    // Card de peso
    const weightColor = analysisData.weightAnalysis.status === 'normal' ? PDF_CONFIG.colors.success : PDF_CONFIG.colors.error;
    this.addColoredCard(
      `PESO: ${PDFUtils.formatWeight(patientData.weight)}`,
      [
        `Percentil: P${analysisData.weightAnalysis.percentile}`,
        `Status: ${analysisData.weightAnalysis.interpretation}`,
        `Classificacao: ${analysisData.weightAnalysis.classification}`
      ],
      weightColor
    );

    // Card de altura
    const heightColor = analysisData.heightAnalysis.status === 'normal' ? PDF_CONFIG.colors.success : PDF_CONFIG.colors.error;
    this.addColoredCard(
      `ALTURA: ${PDFUtils.formatHeight(patientData.height)}`,
      [
        `Percentil: P${analysisData.heightAnalysis.percentile}`,
        `Status: ${analysisData.heightAnalysis.interpretation}`
      ],
      heightColor
    );

    // Card de perímetro cefálico
    const headColor = analysisData.headCircumferenceAnalysis.status === 'normal' ? PDF_CONFIG.colors.success : PDF_CONFIG.colors.error;
    this.addColoredCard(
      `PERIMETRO CEFALICO: ${PDFUtils.formatHeight(patientData.headCircumference)}`,
      [
        `Percentil: P${analysisData.headCircumferenceAnalysis.percentile}`,
        `Status: ${analysisData.headCircumferenceAnalysis.interpretation}`
      ],
      headColor
    );
  }

  /**
   * Renderiza recomendações de suplementação
   */
  renderSupplementation(supplementationData: SupplementationData): void {
    if (supplementationData.recommendations.length === 0) return;

    this.addSectionTitle(PDF_CONFIG.texts.sections.supplementation, PDF_CONFIG.texts.icons.supplementation);

    supplementationData.recommendations.forEach(rec => {
      const color = rec.vitamin === 'D' ? PDF_CONFIG.colors.info :
                   rec.vitamin === 'Ferro' ? PDF_CONFIG.colors.error :
                   PDF_CONFIG.colors.purple;

      // Organizar informações sem duplicação
      const content: string[] = [];

      if (rec.dosage && rec.dosage !== 'Conforme orientacao medica') {
        content.push(`Dosagem: ${rec.dosage}`);
      }

      if (rec.duration && rec.duration !== 'Conforme orientacao medica') {
        content.push(`Duracao: ${rec.duration}`);
      }

      if (rec.notes && rec.notes !== 'Administrar conforme orientacao') {
        content.push(`Observacoes: ${rec.notes}`);
      }

      // Se não há informações específicas, usar texto padrão
      if (content.length === 0) {
        content.push('Conforme orientacao medica');
      }

      this.addColoredCard(
        `${rec.vitamin === 'D' ? 'Vitamina D' : rec.vitamin === 'A' ? 'Vitamina A' : 'Ferro'}`,
        content,
        color
      );
    });
  }

  /**
   * Renderiza calendário vacinal
   */
  renderVaccines(vaccineData: VaccineData): void {
    this.checkPageBreak(30);
    this.addSectionTitle(PDF_CONFIG.texts.sections.vaccines, PDF_CONFIG.texts.icons.vaccines);

    if (vaccineData.applied.length > 0) {
      this.addColoredCard(
        'VACINAS APLICADAS',
        vaccineData.applied.map(v => `- ${v.name} (${v.age})`),
        PDF_CONFIG.colors.success
      );
    }

    if (vaccineData.upcoming.length > 0) {
      this.addColoredCard(
        'PROXIMAS VACINAS',
        vaccineData.upcoming.map(v => `- ${v.name} (${v.age})`),
        PDF_CONFIG.colors.warning
      );
    }
  }

  /**
   * Renderiza rodapé
   */
  renderFooter(): void {
    this.checkPageBreak(20);
    this.currentY = PDF_CONFIG.page.height - 30;
    this.pdf.setFontSize(PDF_CONFIG.fonts.tiny);
    this.pdf.setFont('helvetica', 'italic');
    this.pdf.setTextColor(...PDF_CONFIG.colors.gray);
    
    const footer = PDFUtils.cleanText(PDF_CONFIG.texts.footer);
    const contentWidth = PDF_CONFIG.page.width - (PDF_CONFIG.page.margin * 2);
    const footerLines = this.pdf.splitTextToSize(footer, contentWidth);
    this.pdf.text(footerLines, PDF_CONFIG.page.margin, this.currentY);
  }
}
