/* Estilos específicos para o relatório PDF - OTIMIZADO PARA MENOR PESO */
.pdf-report {
  font-family: Arial, sans-serif; /* Simplificado */
  font-size: 11px; /* Menor para reduzir espaço */
  line-height: 1.3; /* Mais compacto */
  color: #333;
  background: white;
  position: relative; /* Para marca d'água */
}

/* NOVO: Marca d'água com logo */
.pdf-report::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  background-image: url('/faviconx.webp');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.05; /* Muito transparente */
  z-index: 0;
  pointer-events: none;
}

/* Garantir que o conteúdo fique acima da marca d'água */
.pdf-report > * {
  position: relative;
  z-index: 1;
}

.pdf-report h1 {
  font-size: 20px; /* REDUZIDO */
  font-weight: bold;
  color: #1e40af;
  margin-bottom: 6px; /* REDUZIDO */
}

.pdf-report h2 {
  font-size: 14px; /* REDUZIDO */
  font-weight: bold;
  color: #1d4ed8;
  margin-bottom: 8px; /* REDUZIDO */
  padding-bottom: 2px; /* REDUZIDO */
  border-bottom: 1px solid #d1d5db;
}

.pdf-report h3 {
  font-size: 12px; /* REDUZIDO */
  font-weight: 600;
  margin-bottom: 4px; /* REDUZIDO */
}

.pdf-report p {
  margin-bottom: 4px;
}

.pdf-report .grid {
  display: grid;
  gap: 16px;
}

.pdf-report .grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.pdf-report .grid-cols-3 {
  grid-template-columns: repeat(3, 1fr);
}

.pdf-report .section-card {
  padding: 8px; /* REDUZIDO */
  border: 1px solid #e5e7eb;
  border-radius: 4px; /* REDUZIDO */
  margin-bottom: 8px; /* REDUZIDO */
}

/* SIMPLIFICADO: Apenas cores essenciais para reduzir peso */
.pdf-report .bg-red-50 { background-color: #fef2f2; }
.pdf-report .bg-green-50 { background-color: #f0fdf4; }
.pdf-report .bg-blue-50 { background-color: #eff6ff; }
.pdf-report .bg-yellow-50 { background-color: #fefce8; }
.pdf-report .bg-purple-50 { background-color: #faf5ff; }
.pdf-report .bg-amber-50 { background-color: #fffbeb; }
.pdf-report .bg-orange-50 { background-color: #fff7ed; }

/* SIMPLIFICADO: Cores de texto essenciais */
.pdf-report .text-red-700 { color: #b91c1c; }
.pdf-report .text-green-700 { color: #15803d; }
.pdf-report .text-blue-700 { color: #1d4ed8; }
.pdf-report .text-blue-800 { color: #1e40af; }
.pdf-report .text-purple-700 { color: #7c3aed; }
.pdf-report .text-amber-700 { color: #b45309; }
.pdf-report .text-orange-700 { color: #c2410c; }
.pdf-report .text-yellow-800 { color: #92400e; }

.pdf-report .border-b-2 {
  border-bottom: 2px solid;
}

.pdf-report .border-blue-600 {
  border-color: #2563eb;
}

.pdf-report .border-t-2 {
  border-top: 2px solid;
}

.pdf-report .border-gray-300 {
  border-color: #d1d5db;
}

.pdf-report .text-center {
  text-align: center;
}

/* SIMPLIFICADO: Espaçamentos essenciais e reduzidos */
.pdf-report .mb-2 { margin-bottom: 4px; } /* REDUZIDO */
.pdf-report .mb-3 { margin-bottom: 6px; } /* REDUZIDO */
.pdf-report .mb-6 { margin-bottom: 12px; } /* REDUZIDO */
.pdf-report .mb-8 { margin-bottom: 16px; } /* REDUZIDO */
.pdf-report .mt-2 { margin-top: 4px; } /* REDUZIDO */
.pdf-report .mt-8 { margin-top: 16px; } /* REDUZIDO */
.pdf-report .pt-4 { padding-top: 8px; } /* REDUZIDO */
.pdf-report .pb-1 { padding-bottom: 2px; } /* REDUZIDO */
.pdf-report .pb-4 { padding-bottom: 8px; } /* REDUZIDO */

.pdf-report .space-y-1 > * + * { margin-top: 2px; } /* REDUZIDO */
.pdf-report .space-y-2 > * + * { margin-top: 4px; } /* REDUZIDO */
.pdf-report .space-y-4 > * + * { margin-top: 8px; } /* REDUZIDO */

.pdf-report .list-disc {
  list-style-type: disc;
}

.pdf-report .list-inside {
  list-style-position: inside;
}

.pdf-report .text-sm { font-size: 10px; } /* REDUZIDO */
.pdf-report .text-xs { font-size: 9px; } /* REDUZIDO */

.pdf-report .font-bold {
  font-weight: bold;
}

.pdf-report .font-semibold {
  font-weight: 600;
}

/* Estilos para impressão */
@media print {
  .pdf-report {
    font-size: 11px;
    line-height: 1.3;
  }
  
  .pdf-report .section-card {
    break-inside: avoid;
    page-break-inside: avoid;
  }
  
  .pdf-report h2 {
    break-after: avoid;
    page-break-after: avoid;
  }
}

/* Estilos para melhor renderização no canvas */
.pdf-report * {
  box-sizing: border-box;
}

.pdf-report img {
  max-width: 100%;
  height: auto;
}
