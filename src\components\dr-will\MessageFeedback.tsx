import React, { useState } from 'react';
import { ThumbsUp, ThumbsDown, MessageSquare, Send, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { motion, AnimatePresence } from 'framer-motion';

interface MessageFeedbackProps {
  messageId: string;
  onFeedback: (messageId: string, type: 'positive' | 'negative', comment?: string) => void;
}

export const MessageFeedback: React.FC<MessageFeedbackProps> = ({ messageId, onFeedback }) => {
  const [feedbackGiven, setFeedbackGiven] = useState<'positive' | 'negative' | null>(null);
  const [showCommentBox, setShowCommentBox] = useState(false);
  const [comment, setComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleFeedback = async (type: 'positive' | 'negative') => {
    if (feedbackGiven) return; // J<PERSON> deu feedback

    setFeedbackGiven(type);
    
    // Se for negativo, mostrar caixa de comentário
    if (type === 'negative') {
      setShowCommentBox(true);
    } else {
      // Se for positivo, enviar imediatamente
      await submitFeedback(type);
    }
  };

  const submitFeedback = async (type: 'positive' | 'negative', userComment?: string) => {
    setIsSubmitting(true);
    
    try {
      await onFeedback(messageId, type, userComment || comment);
      
      // Fechar caixa de comentário após envio
      if (showCommentBox) {
        setTimeout(() => {
          setShowCommentBox(false);
          setComment('');
        }, 1000);
      }
    } catch (error) {
      console.error('Erro ao enviar feedback:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCommentSubmit = () => {
    if (feedbackGiven) {
      submitFeedback(feedbackGiven, comment);
    }
  };

  const handleSkipComment = () => {
    if (feedbackGiven) {
      submitFeedback(feedbackGiven, '');
    }
  };

  return (
    <div className="mt-4 pt-3 border-t border-gray-100 dark:border-slate-700/50">
      {/* Botões de feedback */}
      <div className="flex items-center justify-between">
        <span className="text-xs text-gray-500 dark:text-gray-400">
          Esta resposta foi útil?
        </span>
        
        <div className="flex items-center gap-2">
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => handleFeedback('positive')}
            disabled={feedbackGiven !== null}
            className={`p-2 rounded-full transition-all duration-200 ${
              feedbackGiven === 'positive'
                ? 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400'
                : feedbackGiven === 'negative'
                ? 'opacity-50 cursor-not-allowed text-gray-400'
                : 'hover:bg-gray-100 dark:hover:bg-slate-700 text-gray-500 dark:text-gray-400 hover:text-green-600 dark:hover:text-green-400'
            }`}
            title="Resposta útil"
          >
            <ThumbsUp className="h-4 w-4" />
          </motion.button>

          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => handleFeedback('negative')}
            disabled={feedbackGiven !== null}
            className={`p-2 rounded-full transition-all duration-200 ${
              feedbackGiven === 'negative'
                ? 'bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400'
                : feedbackGiven === 'positive'
                ? 'opacity-50 cursor-not-allowed text-gray-400'
                : 'hover:bg-gray-100 dark:hover:bg-slate-700 text-gray-500 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400'
            }`}
            title="Resposta não útil"
          >
            <ThumbsDown className="h-4 w-4" />
          </motion.button>
        </div>
      </div>

      {/* Mensagem de agradecimento */}
      <AnimatePresence>
        {feedbackGiven && !showCommentBox && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="mt-2 text-xs text-center"
          >
            {feedbackGiven === 'positive' ? (
              <span className="text-green-600 dark:text-green-400">
                ✅ Obrigado pelo feedback positivo!
              </span>
            ) : (
              <span className="text-blue-600 dark:text-blue-400">
                📝 Feedback registrado. Obrigado por nos ajudar a melhorar!
              </span>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Caixa de comentário para feedback negativo */}
      <AnimatePresence>
        {showCommentBox && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mt-3 space-y-3"
          >
            <div className="bg-gray-50 dark:bg-slate-800/50 rounded-lg p-3 border border-gray-200 dark:border-slate-700">
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-center gap-2">
                  <MessageSquare className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Como podemos melhorar?
                  </span>
                </div>
                <button
                  onClick={() => setShowCommentBox(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
              
              <Textarea
                value={comment}
                onChange={(e) => setComment(e.target.value)}
                placeholder="Conte-nos o que poderia ser melhor na resposta..."
                className="min-h-[80px] text-sm resize-none"
                maxLength={500}
              />
              
              <div className="flex items-center justify-between mt-3">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {comment.length}/500 caracteres
                </span>
                
                <div className="flex gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleSkipComment}
                    disabled={isSubmitting}
                    className="text-xs"
                  >
                    Pular
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleCommentSubmit}
                    disabled={isSubmitting}
                    className="text-xs"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin mr-1" />
                        Enviando...
                      </>
                    ) : (
                      <>
                        <Send className="h-3 w-3 mr-1" />
                        Enviar
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
