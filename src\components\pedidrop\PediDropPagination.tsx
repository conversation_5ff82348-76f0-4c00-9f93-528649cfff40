import React from 'react';
import { motion } from 'framer-motion';
import { ChevronLeft, ChevronRight, MoreHorizontal, Droplets } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface PediDropPaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
}

export const PediDropPagination: React.FC<PediDropPaginationProps> = ({
  currentPage,
  totalPages,
  totalItems,
  itemsPerPage,
  onPageChange
}) => {
  // Calcular range de itens sendo exibidos
  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);

  // Gerar array de páginas para exibir
  const getPageNumbers = () => {
    const pages: (number | string)[] = [];
    const maxVisiblePages = 7;

    if (totalPages <= maxVisiblePages) {
      // Se temos poucas páginas, mostrar todas
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Lógica para páginas com ellipsis
      if (currentPage <= 4) {
        // Início: 1, 2, 3, 4, 5, ..., last
        for (let i = 1; i <= 5; i++) {
          pages.push(i);
        }
        pages.push('...');
        pages.push(totalPages);
      } else if (currentPage >= totalPages - 3) {
        // Fim: 1, ..., last-4, last-3, last-2, last-1, last
        pages.push(1);
        pages.push('...');
        for (let i = totalPages - 4; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        // Meio: 1, ..., current-1, current, current+1, ..., last
        pages.push(1);
        pages.push('...');
        for (let i = currentPage - 1; i <= currentPage + 1; i++) {
          pages.push(i);
        }
        pages.push('...');
        pages.push(totalPages);
      }
    }

    return pages;
  };

  const pageNumbers = getPageNumbers();

  const handlePrevious = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  };

  const handleNext = () => {
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1);
    }
  };

  const handlePageClick = (page: number | string) => {
    if (typeof page === 'number' && page !== currentPage) {
      onPageChange(page);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-8 p-4 bg-white dark:bg-slate-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm"
    >
      {/* Informações dos Resultados */}
      <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
        <Droplets className="h-4 w-4 text-blue-600 dark:text-blue-400" />
        <span>
          Exibindo <span className="font-semibold text-gray-900 dark:text-gray-100">{startItem}</span> a{' '}
          <span className="font-semibold text-gray-900 dark:text-gray-100">{endItem}</span> de{' '}
          <span className="font-semibold text-gray-900 dark:text-gray-100">{totalItems}</span> PediDrops
        </span>
      </div>

      {/* Controles de Paginação */}
      <div className="flex items-center gap-1">
        {/* Botão Anterior */}
        <Button
          variant="outline"
          size="sm"
          onClick={handlePrevious}
          disabled={currentPage === 1}
          className={cn(
            "flex items-center gap-1 px-3 py-2",
            currentPage === 1 && "opacity-50 cursor-not-allowed"
          )}
        >
          <ChevronLeft className="h-4 w-4" />
          <span className="hidden sm:inline">Anterior</span>
        </Button>

        {/* Números das Páginas */}
        <div className="flex items-center gap-1 mx-2">
          {pageNumbers.map((page, index) => {
            if (page === '...') {
              return (
                <div
                  key={`ellipsis-${index}`}
                  className="flex items-center justify-center w-8 h-8 text-gray-400"
                >
                  <MoreHorizontal className="h-4 w-4" />
                </div>
              );
            }

            const pageNumber = page as number;
            const isCurrentPage = pageNumber === currentPage;

            return (
              <motion.div
                key={pageNumber}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  variant={isCurrentPage ? "default" : "outline"}
                  size="sm"
                  onClick={() => handlePageClick(pageNumber)}
                  className={cn(
                    "w-8 h-8 p-0 text-sm font-medium",
                    isCurrentPage && "bg-blue-600 hover:bg-blue-700 text-white shadow-md",
                    !isCurrentPage && "hover:bg-blue-50 dark:hover:bg-blue-900/20"
                  )}
                >
                  {pageNumber}
                </Button>
              </motion.div>
            );
          })}
        </div>

        {/* Botão Próximo */}
        <Button
          variant="outline"
          size="sm"
          onClick={handleNext}
          disabled={currentPage === totalPages}
          className={cn(
            "flex items-center gap-1 px-3 py-2",
            currentPage === totalPages && "opacity-50 cursor-not-allowed"
          )}
        >
          <span className="hidden sm:inline">Próximo</span>
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>

      {/* Navegação Rápida (Mobile) */}
      <div className="sm:hidden flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
        <span>Página</span>
        <span className="font-semibold text-blue-600 dark:text-blue-400">
          {currentPage}
        </span>
        <span>de</span>
        <span className="font-semibold">
          {totalPages}
        </span>
      </div>
    </motion.div>
  );
};
