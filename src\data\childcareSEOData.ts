export interface ChildcareSEOData {
  title: string;
  description: string;
  slug: string;
  category: 'overview' | 'growth' | 'vaccines' | 'formulas' | 'supplementation' | 'development' | 'main';
  keywords: string[];
  clinicalUse: string;
  targetAge: string;
  features: string[];
  benefits: string[];
  clinicalSignificance: string;
  relatedTopics: string[];
}

export const CHILDCARE_SEO_DATA: Record<string, ChildcareSEOData> = {
  'main': {
    title: 'Puericultura',
    description: 'Ferramentas essenciais para acompanhamento do crescimento e desenvolvimento infantil',
    slug: 'main',
    category: 'main',
    keywords: ['puericultura', 'pediatria', 'crescimento infantil', 'desenvolvimento', 'acompanhamento pediátrico'],
    clinicalUse: 'Plataforma completa para acompanhamento pediátrico com ferramentas especializadas para crescimento, desenvolvimento e cuidados infantis.',
    targetAge: 'recém-nascidos, lactentes, crianças e adolescentes',
    features: ['<PERSON>urvas de Crescimento', 'Calendário Vacinal', 'Fórmulas Infantis', 'Suplementação', 'DNPM', 'Visão Geral do Paciente'],
    benefits: ['Acompanhamento completo', 'Ferramentas especializadas', 'Orientações baseadas em evidências', 'Interface intuitiva'],
    clinicalSignificance: 'Facilita o acompanhamento pediátrico sistemático e melhora a qualidade dos cuidados infantis.',
    relatedTopics: ['crescimento infantil', 'desenvolvimento neuropsicomotor', 'vacinação', 'nutrição infantil', 'consulta pediátrica']
  },

  'patient-overview': {
    title: 'Visão Geral do Paciente',
    description: 'Análise completa e personalizada do desenvolvimento infantil com recomendações específicas para cada paciente',
    slug: 'patient-overview',
    category: 'overview',
    keywords: ['visão geral paciente', 'acompanhamento pediátrico', 'análise desenvolvimento', 'consulta puericultura'],
    clinicalUse: 'Ferramenta para análise integrada do desenvolvimento infantil com recomendações personalizadas baseadas na idade e características do paciente.',
    targetAge: 'recém-nascidos a adolescentes',
    features: ['Análise personalizada', 'Recomendações específicas', 'Acompanhamento integrado', 'Relatórios detalhados'],
    benefits: ['Visão holística do paciente', 'Orientações personalizadas', 'Otimização da consulta', 'Melhoria do cuidado'],
    clinicalSignificance: 'Permite acompanhamento sistemático e personalizado do desenvolvimento infantil.',
    relatedTopics: ['consulta pediátrica', 'desenvolvimento infantil', 'acompanhamento longitudinal', 'cuidados personalizados']
  },

  'curva-de-crescimento': {
    title: 'Curvas de Crescimento',
    description: 'Curvas de crescimento pediátricas para acompanhamento do desenvolvimento físico infantil',
    slug: 'curva-de-crescimento',
    category: 'growth',
    keywords: ['curvas crescimento', 'percentil crescimento', 'peso altura', 'crescimento infantil', 'desenvolvimento físico'],
    clinicalUse: 'Avaliação do crescimento físico através de curvas padronizadas da OMS para peso, altura e perímetro cefálico.',
    targetAge: 'recém-nascidos a 19 anos',
    features: ['Curvas OMS', 'Percentis', 'Z-Score', 'Gráficos interativos', 'Acompanhamento longitudinal'],
    benefits: ['Detecção precoce de alterações', 'Monitoramento preciso', 'Orientação nutricional', 'Seguimento adequado'],
    clinicalSignificance: 'Fundamental para detecção precoce de distúrbios do crescimento e orientação nutricional.',
    relatedTopics: ['crescimento infantil', 'nutrição pediátrica', 'desenvolvimento físico', 'percentil peso', 'percentil altura']
  },

  'calendario-vacinal': {
    title: 'Calendário Vacinal',
    description: 'Calendário vacinal completo e atualizado conforme recomendações do Ministério da Saúde',
    slug: 'calendario-vacinal',
    category: 'vaccines',
    keywords: ['calendário vacinal', 'vacinas criança', 'imunização', 'esquema vacinal', 'vacinação pediátrica'],
    clinicalUse: 'Guia completo de vacinação pediátrica com esquemas, idades recomendadas e orientações específicas.',
    targetAge: 'recém-nascidos a adolescentes',
    features: ['Esquema completo', 'Idades recomendadas', 'Contraindicações', 'Eventos adversos', 'Atualizações constantes'],
    benefits: ['Prevenção de doenças', 'Proteção comunitária', 'Orientação precisa', 'Acompanhamento sistemático'],
    clinicalSignificance: 'Essencial para prevenção de doenças imunopreveníveis e proteção da saúde infantil.',
    relatedTopics: ['imunização infantil', 'prevenção doenças', 'saúde pública', 'proteção vacinal', 'esquema básico']
  },

  'formulas': {
    title: 'Fórmulas Infantis',
    description: 'Guia completo sobre fórmulas infantis, tipos, indicações e orientações nutricionais',
    slug: 'formulas',
    category: 'formulas',
    keywords: ['fórmulas infantis', 'leite artificial', 'nutrição infantil', 'alimentação bebê', 'fórmula láctea'],
    clinicalUse: 'Orientações sobre tipos de fórmulas infantis, indicações específicas e recomendações nutricionais.',
    targetAge: 'recém-nascidos a 2 anos',
    features: ['Tipos de fórmulas', 'Indicações específicas', 'Preparo adequado', 'Orientações nutricionais', 'FAQ especializada'],
    benefits: ['Nutrição adequada', 'Orientação especializada', 'Segurança alimentar', 'Desenvolvimento saudável'],
    clinicalSignificance: 'Fundamental para orientação nutricional quando o aleitamento materno não é possível.',
    relatedTopics: ['nutrição infantil', 'aleitamento artificial', 'desenvolvimento nutricional', 'alimentação complementar', 'APLV']
  },

  'suplementacao-infantil': {
    title: 'Suplementação Infantil',
    description: 'Orientações automáticas para suplementação de vitaminas e minerais conforme necessidades da criança',
    slug: 'suplementacao-infantil',
    category: 'supplementation',
    keywords: ['suplementação infantil', 'vitaminas criança', 'ferro infantil', 'vitamina d', 'suplementos pediátricos'],
    clinicalUse: 'Calculadora automática para suplementação de vitaminas e minerais baseada na idade, peso e necessidades específicas.',
    targetAge: 'recém-nascidos a adolescentes',
    features: ['Cálculo automático', 'Dosagens personalizadas', 'Múltiplos suplementos', 'Orientações específicas', 'Contraindicações'],
    benefits: ['Prevenção deficiências', 'Dosagem precisa', 'Orientação individualizada', 'Desenvolvimento adequado'],
    clinicalSignificance: 'Previne deficiências nutricionais e garante desenvolvimento adequado através de suplementação orientada.',
    relatedTopics: ['vitamina D', 'ferro infantil', 'ácido fólico', 'vitaminas', 'minerais', 'deficiência nutricional']
  },

  'dnpm': {
    title: 'Desenvolvimento Neuropsicomotor (DNPM)',
    description: 'Marcos do desenvolvimento neuropsicomotor para avaliação do desenvolvimento infantil',
    slug: 'dnpm',
    category: 'development',
    keywords: ['desenvolvimento neuropsicomotor', 'marcos desenvolvimento', 'dnpm', 'desenvolvimento motor', 'desenvolvimento cognitivo'],
    clinicalUse: 'Avaliação sistemática dos marcos do desenvolvimento neuropsicomotor para detecção precoce de atrasos.',
    targetAge: 'recém-nascidos a 6 anos',
    features: ['Marcos por idade', 'Avaliação sistemática', 'Sinais de alerta', 'Orientações específicas', 'Acompanhamento longitudinal'],
    benefits: ['Detecção precoce', 'Intervenção oportuna', 'Orientação familiar', 'Desenvolvimento adequado'],
    clinicalSignificance: 'Fundamental para detecção precoce de atrasos do desenvolvimento e intervenção adequada.',
    relatedTopics: ['desenvolvimento motor', 'desenvolvimento cognitivo', 'marcos motores', 'atraso desenvolvimento', 'estimulação precoce']
  }
};
