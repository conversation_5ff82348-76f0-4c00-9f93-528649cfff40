import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

interface HomepageData {
  categories: string[];
}

export const useHomepageData = () => {
  return useQuery({
    queryKey: ['homepage-seo-data'],
    queryFn: async (): Promise<HomepageData> => {
      // Buscar categorias para keywords SEO
      const { data: categoriesData, error: categoriesError } = await supabase
        .from('pedbook_medication_categories')
        .select('name')
        .order('name');

      if (categoriesError) throw categoriesError;

      const categories = categoriesData?.map(cat => cat.name) || [];

      return {
        categories
      };
    },
    staleTime: 60 * 60 * 1000, // 1 hora
    gcTime: 24 * 60 * 60 * 1000, // 24 horas
  });
};
