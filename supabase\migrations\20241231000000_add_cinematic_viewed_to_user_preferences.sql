-- Ad<PERSON><PERSON><PERSON> coluna para controlar se o usuário já viu a experiência cinematográfica
ALTER TABLE user_preferences 
ADD COLUMN cinematic_viewed BOOLEAN DEFAULT FALSE;

-- Comentário explicativo
COMMENT ON COLUMN user_preferences.cinematic_viewed IS 'Indica se o usuário já visualizou a experiência cinematográfica de boas-vindas';

-- Criar índice para otimizar consultas
CREATE INDEX IF NOT EXISTS idx_user_preferences_cinematic_viewed 
ON user_preferences(cinematic_viewed) 
WHERE cinematic_viewed = FALSE;
