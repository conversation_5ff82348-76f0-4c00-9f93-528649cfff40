import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { usePediDropFeedbackStats } from '@/hooks/usePediDropFeedbackStats';
import {
  TrendingUp,
  TrendingDown,
  Meh,
  Star,
  MessageSquare,
  Users,
  BarChart3,
  Clock
} from 'lucide-react';

export const PediDropFeedbackStats: React.FC = () => {
  const { data: stats, isLoading, isError } = usePediDropFeedbackStats();

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardContent className="p-4">
              <Skeleton className="h-4 w-20 mb-2" />
              <Skeleton className="h-8 w-16 mb-2" />
              <Skeleton className="h-3 w-24" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (isError || !stats) {
    return (
      <Card className="mb-6">
        <CardContent className="p-4">
          <p className="text-red-600 dark:text-red-400">
            Erro ao carregar estatísticas de feedback
          </p>
        </CardContent>
      </Card>
    );
  }

  const feedbackTypePercentages = {
    continue: stats.total_feedback > 0 ? (stats.continue_count / stats.total_feedback) * 100 : 0,
    improve: stats.total_feedback > 0 ? (stats.improve_count / stats.total_feedback) * 100 : 0,
    discontinue: stats.total_feedback > 0 ? (stats.discontinue_count / stats.total_feedback) * 100 : 0,
  };

  return (
    <div className="space-y-6 mb-6">
      {/* Cards de Estatísticas Principais */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Total de Feedbacks */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="bg-blue-100 dark:bg-blue-900/30 p-2 rounded-lg">
                <Users className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Total Feedbacks</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {stats.total_feedback}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Avaliação Média */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="bg-yellow-100 dark:bg-yellow-900/30 p-2 rounded-lg">
                <Star className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Avaliação Média</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {stats.average_rating.toFixed(1)}
                </p>
                <div className="flex items-center gap-1">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Star
                      key={i}
                      className={`h-3 w-3 ${
                        i < Math.round(stats.average_rating)
                          ? 'text-yellow-400 fill-current'
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Continue */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="bg-green-100 dark:bg-green-900/30 p-2 rounded-lg">
                <TrendingUp className="h-5 w-5 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Continue Assim</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {stats.continue_count}
                </p>
                <Badge variant="secondary" className="text-xs">
                  {feedbackTypePercentages.continue.toFixed(1)}%
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Melhorar */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="bg-yellow-100 dark:bg-yellow-900/30 p-2 rounded-lg">
                <Meh className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Pode Melhorar</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {stats.improve_count}
                </p>
                <Badge variant="secondary" className="text-xs">
                  {feedbackTypePercentages.improve.toFixed(1)}%
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Feedback por Post */}
      {stats.feedback_by_post.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Feedback por Post
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {stats.feedback_by_post.slice(0, 5).map((post) => (
                <div key={post.post_id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-gray-900 dark:text-gray-100 truncate">
                      {post.post_id === 'general' ? 'Feedback Geral' : `Post: ${post.post_id}`}
                    </h4>
                    <div className="flex items-center gap-4 mt-1">
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        {post.feedback_count} feedbacks
                      </span>
                      <div className="flex items-center gap-1">
                        <Star className="h-3 w-3 text-yellow-400 fill-current" />
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          {post.average_rating.toFixed(1)}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    {post.continue_count > 0 && (
                      <Badge variant="secondary" className="bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300">
                        +{post.continue_count}
                      </Badge>
                    )}
                    {post.improve_count > 0 && (
                      <Badge variant="secondary" className="bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300">
                        ~{post.improve_count}
                      </Badge>
                    )}
                    {post.discontinue_count > 0 && (
                      <Badge variant="secondary" className="bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300">
                        -{post.discontinue_count}
                      </Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Comentários Recentes */}
      {stats.recent_comments.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              Comentários Recentes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stats.recent_comments.slice(0, 3).map((comment) => (
                <div key={comment.id} className="border-l-4 border-blue-500 pl-4 py-2">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Badge 
                        variant="secondary" 
                        className={
                          comment.feedback_type === 'continue' 
                            ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300'
                            : comment.feedback_type === 'improve'
                            ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300'
                            : 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300'
                        }
                      >
                        {comment.feedback_type === 'continue' ? 'Continue' : 
                         comment.feedback_type === 'improve' ? 'Melhorar' : 'Repensar'}
                      </Badge>
                      <div className="flex items-center gap-1">
                        {Array.from({ length: comment.rating }).map((_, i) => (
                          <Star key={i} className="h-3 w-3 text-yellow-400 fill-current" />
                        ))}
                      </div>
                    </div>
                    <div className="flex items-center gap-1 text-xs text-gray-500">
                      <Clock className="h-3 w-3" />
                      {new Date(comment.created_at).toLocaleDateString('pt-BR')}
                    </div>
                  </div>
                  <p className="text-sm text-gray-700 dark:text-gray-300 mb-1">
                    "{comment.message}"
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Post: {comment.post_id === 'general' ? 'Feedback Geral' : comment.post_id}
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
