import React from 'react';
import { ExternalLink, Search, Calendar, Globe } from 'lucide-react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface SearchResult {
  title: string;
  link: string;
  snippet: string;
  position: number;
  date?: string;
  attributes?: Record<string, any>;
}

interface SearchResultsCardProps {
  results: SearchResult[];
  className?: string;
}

interface SearchResultsCardState {
  showAll: boolean;
}

export const SearchResultsCard: React.FC<SearchResultsCardProps> = ({
  results,
  className = ""
}) => {
  const [showAll, setShowAll] = React.useState(false);

  if (!results || results.length === 0) return null;

  const handleLinkClick = (url: string, title: string) => {
    // Log para analytics
    console.log('🔗 [DrWill] Link externo clicado:', { url, title });

    // Garantir abertura em nova aba/janela
    try {
      // Método 1: window.open (mais confi<PERSON>)
      const newWindow = window.open(url, '_blank', 'noopener,noreferrer');

      // Fallback se window.open for bloqueado
      if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
        // Método 2: Criar link temporário
        const link = document.createElement('a');
        link.href = url;
        link.target = '_blank';
        link.rel = 'noopener noreferrer';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } catch (error) {
      console.warn('⚠️ [DrWill] Erro ao abrir link:', error);
      // Fallback final: navegar na mesma aba
      window.location.href = url;
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return null;
    
    try {
      // Tentar diferentes formatos de data
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return dateString; // Retornar string original se não conseguir parsear
      }
      
      return date.toLocaleDateString('pt-BR', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return dateString;
    }
  };

  const getDomainFromUrl = (url: string) => {
    try {
      const domain = new URL(url).hostname;
      return domain.replace('www.', '');
    } catch {
      return 'Link externo';
    }
  };

  const truncateSnippet = (snippet: string, maxLength: number = 150) => {
    if (snippet.length <= maxLength) return snippet;
    return snippet.substring(0, maxLength).trim() + '...';
  };

  // Controlar quantos resultados mostrar
  const maxInitialResults = 5;
  const displayResults = showAll ? results : results.slice(0, maxInitialResults);
  const hasMoreResults = results.length > maxInitialResults;

  // Debug detalhado
  console.log('📊 [SearchResultsCard] Debug completo:', {
    totalResults: results.length,
    displayResults: displayResults.length,
    showAll,
    hasMoreResults,
    shouldShowMore: hasMoreResults && !showAll,
    remainingCount: results.length - maxInitialResults,
    allTitles: results.map((r, i) => `${i + 1}. ${r.title?.substring(0, 30)}...`),
    displayTitles: displayResults.map((r, i) => `${i + 1}. ${r.title?.substring(0, 30)}...`)
  });

  return (
    <div className={`mt-3 ${className}`}>
      {/* Header mais discreto */}
      <div className="flex items-center gap-2 mb-3 px-1">
        <Search className="h-4 w-4 text-gray-400" />
        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
          Fontes encontradas
        </span>
        <span className="text-xs text-gray-400 ml-auto">
          {showAll ? results.length : `${displayResults.length} de ${results.length}`}
        </span>
      </div>

      {/* Lista compacta */}
      <div className="space-y-2">
        {displayResults.map((result, index) => (
          <div
            key={index}
            className="bg-gray-50/50 dark:bg-slate-800/30 rounded-lg p-3 border border-gray-100 dark:border-slate-700/50 hover:bg-gray-100/50 dark:hover:bg-slate-700/30 transition-colors"
          >
            {/* Título compacto */}
            <div className="mb-2">
              <h4 className="text-sm font-medium text-gray-800 dark:text-gray-200 leading-tight line-clamp-2">
                {result.title}
              </h4>
            </div>

            {/* Snippet mais curto */}
            <p className="text-xs text-gray-600 dark:text-gray-400 mb-2 leading-relaxed line-clamp-2">
              {truncateSnippet(result.snippet, 100)}
            </p>

            {/* Footer compacto */}
            <div className="flex items-center justify-between text-xs">
              <div className="flex items-center gap-2 text-gray-500 dark:text-gray-400">
                <span className="flex items-center gap-1">
                  <Globe className="h-3 w-3" />
                  {getDomainFromUrl(result.link)}
                </span>

                {result.date && (
                  <span className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    {formatDate(result.date)}
                  </span>
                )}
              </div>

              <button
                onClick={() => handleLinkClick(result.link, result.title)}
                className="inline-flex items-center gap-1 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium transition-colors bg-blue-50 dark:bg-blue-900/20 px-2 py-1 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/40"
                title="Abrir link em nova aba"
              >
                <span className="text-xs">Abrir</span>
                <ExternalLink className="h-3 w-3" />
              </button>
            </div>
          </div>
        ))}

        {hasMoreResults && !showAll && (
          <div className="text-center pt-2">
            <button
              onClick={() => {
                setShowAll(true);
                console.log('🔍 [SearchResultsCard] Expandindo para mostrar todos os resultados');
              }}
              className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium bg-blue-50 dark:bg-blue-900/20 px-3 py-1 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/40 transition-colors"
            >
              +{results.length - maxInitialResults} mais resultados
            </button>
          </div>
        )}

        {showAll && hasMoreResults && (
          <div className="text-center pt-2">
            <button
              onClick={() => {
                setShowAll(false);
                console.log('🔍 [SearchResultsCard] Recolhendo para mostrar apenas os primeiros resultados');
              }}
              className="text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 font-medium"
            >
              Mostrar menos
            </button>
          </div>
        )}
      </div>
    </div>
  );
};
