import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Message } from '@/types/chat';
import { useToast } from '@/hooks/use-toast';
import { removeMarkdown } from '@/lib/utils';
import { useAuth } from '@/hooks/useAuth';

interface Thread {
  id: string;
  title: string;
  lastMessage: string;
  createdAt: Date;
}

interface ChatMetadata {
  threadId: string;
  [key: string]: any;
}

export const useChatHistory = (activeThreadId?: string) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [threads, setThreads] = useState<Thread[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [threadsCache, setThreadsCache] = useState<Map<string, Thread[]>>(new Map());
  const [messagesCache, setMessagesCache] = useState<Map<string, Message[]>>(new Map());
  const [forceUpdate, setForceUpdate] = useState(0);
  const [isCreatingThread, setIsCreatingThread] = useState(false);
  const [hasMoreThreads, setHasMoreThreads] = useState(true);
  const [threadsPage, setThreadsPage] = useState(0);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const { toast } = useToast();
  const { user } = useAuth();

  const loadThreads = useCallback(async (forceReload = false) => {
    const cacheKey = `threads_${user?.id}`;

    // Verificar cache primeiro (mas não se for reload forçado ou criando thread)
    if (!forceReload && !isCreatingThread && threadsCache.has(cacheKey)) {
      const cachedThreads = threadsCache.get(cacheKey)!;
      setThreads(cachedThreads);
      return;
    }

    const startTime = performance.now();

    try {
      // CORREÇÃO: Tentar primeiro a função RPC com tratamento melhor de erro
      let threadsData = null;
      let rpcError = null;

      try {
        const { data, error } = await supabase
          .rpc('get_user_chat_threads', {
            p_user_id: user?.id,
            p_limit: 20,
            p_offset: 0
          });

        if (error) {
          rpcError = error;
        } else {
          threadsData = data;
        }
      } catch (err) {
        rpcError = err;
      }

      if (rpcError || !threadsData) {
        // Fallback para query manual se a função RPC falhar
        console.warn('RPC function failed, using fallback query:', rpcError);
        const { data: fallbackData, error: fallbackError } = await supabase
          .from('pedbook_chat_history')
          .select(`
            metadata,
            created_at,
            content,
            role
          `)
          .eq('user_id', user?.id)
          .not('metadata->threadId', 'is', null)
          .order('created_at', { ascending: false })
          .limit(200); // Reduzido drasticamente

        console.log(`📊 [ChatHistory] Fallback query result: ${fallbackData?.length || 0} mensagens encontradas`);

        if (fallbackError) throw fallbackError;

        // Processar threads de forma otimizada
        const threadMap = new Map<string, {
          lastMessage: string;
          createdAt: Date;
          title: string;
          messageCount: number;
        }>();

        fallbackData.forEach((msg) => {
          const metadata = msg.metadata as ChatMetadata;
          const threadId = metadata?.threadId;
          if (!threadId) return;

          const existing = threadMap.get(threadId);
          const msgDate = new Date(msg.created_at);

          if (!existing || msgDate > existing.createdAt) {
            // Gerar título apenas da primeira mensagem do usuário encontrada
            const title = msg.role === 'user' && msg.content
              ? removeMarkdown(msg.content.slice(0, 50))
              : existing?.title || 'Nova conversa';

            threadMap.set(threadId, {
              lastMessage: msg.content.slice(0, 100),
              createdAt: msgDate,
              title,
              messageCount: (existing?.messageCount || 0) + 1
            });
          }
        });

        const threadList: Thread[] = Array.from(threadMap.entries()).map(([id, data]) => ({
          id,
          title: data.title,
          lastMessage: data.lastMessage,
          createdAt: data.createdAt,
        }));

        threadList.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
        setThreads(threadList);

        console.log(`📊 [ChatHistory] Fallback executado - ${threadList.length} threads processados`);

        // Salvar no cache
        setThreadsCache(prev => new Map(prev.set(cacheKey, threadList)));
      } else {
        // Usar dados da função RPC otimizada
        const threadList: Thread[] = threadsData.map((thread: any) => ({
          id: thread.thread_id,
          title: thread.title,
          lastMessage: thread.last_message,
          createdAt: new Date(thread.last_message_date),
        }));

        console.log(`📊 [ChatHistory] RPC executado com sucesso - ${threadList.length} threads carregados`);

        setThreads(threadList);

        // Salvar no cache
        setThreadsCache(prev => new Map(prev.set(cacheKey, threadList)));
      }

      const totalTime = performance.now() - startTime;
      console.log(`✅ [ChatHistory] Threads carregados em ${totalTime.toFixed(2)}ms`);
      console.log(`📊 [ChatHistory] Total de threads carregados: ${threads.length}`);

    } catch (error) {
      const errorTime = performance.now() - startTime;
      console.error(`❌ [ChatHistory] Erro após ${errorTime.toFixed(2)}ms:`, error);

      // Limpar cache em caso de erro
      setThreadsCache(prev => {
        const newCache = new Map(prev);
        newCache.delete(cacheKey);
        return newCache;
      });

      toast({
        title: "Erro ao carregar conversas",
        description: "Ocorreu um erro ao carregar o histórico de conversas.",
        variant: "destructive",
      });
    }
  }, [toast, user?.id, threadsCache, isCreatingThread]);

  const deleteAllThreads = async () => {
    if (!user?.id) return;

    try {
      const { error } = await supabase
        .from('pedbook_chat_history')
        .delete()
        .eq('user_id', user.id);

      if (error) throw error;

      setThreads([]);
      setMessages([]);

      toast({
        title: "Conversas excluídas",
        description: "Todas as conversas foram excluídas com sucesso.",
      });
    } catch (error) {
      toast({
        title: "Erro ao excluir conversas",
        description: "Ocorreu um erro ao excluir as conversas.",
        variant: "destructive",
      });
    }
  };

  const loadMessages = useCallback(async (threadId: string) => {
    // Verificar cache primeiro
    if (messagesCache.has(threadId)) {
      const cachedMessages = messagesCache.get(threadId)!;
      setMessages(cachedMessages);
      return;
    }

    const startTime = performance.now();

    try {
      // OTIMIZAÇÃO: Usar tabela direta em vez da view para melhor performance
      const { data: messagesData, error } = await supabase
        .from('pedbook_chat_history')
        .select('id, role, content, created_at, metadata')
        .eq('user_id', user?.id)
        .eq('metadata->>threadId', threadId)
        .order('created_at', { ascending: true })
        .limit(100); // Limitar mensagens por thread

      if (error) throw error;

      const formattedMessages = messagesData.map((msg): Message => {
        const metadata = msg.metadata as ChatMetadata;
        return {
          role: msg.role as 'user' | 'assistant',
          content: msg.content,
          timestamp: new Date(msg.created_at),
          image_url: metadata?.image_url ? metadata.image_url : undefined,
          file_url: typeof metadata?.file_url === "string" ? metadata.file_url : undefined,
          file_type: typeof metadata?.file_type === "string" ? metadata.file_type : undefined,
          threadId: threadId,
        };
      });

      setMessages(formattedMessages);

      // Salvar no cache
      setMessagesCache(prev => new Map(prev.set(threadId, formattedMessages)));

      const totalTime = performance.now() - startTime;
      console.log(`✅ [ChatHistory] Mensagens carregadas em ${totalTime.toFixed(2)}ms`);

    } catch (error) {
      const errorTime = performance.now() - startTime;
      console.error(`❌ [ChatHistory] Erro ao carregar mensagens após ${errorTime.toFixed(2)}ms:`, error);
      toast({
        title: "Erro ao carregar mensagens",
        description: "Ocorreu um erro ao carregar as mensagens da conversa.",
        variant: "destructive",
      });
    }
  }, [toast, user?.id, messagesCache]);

  const saveMessage = async (message: Message, threadId: string) => {
    // Verificar se é a primeira mensagem do thread
    const isFirstMessageOfThread = !threads.some(t => t.id === threadId);

    // Only set loading for assistant messages (AI responses)
    if (message.role === 'assistant') {
      setIsLoading(true);
    }

    // Criar uma cópia da mensagem para garantir que ela não seja modificada
    const messageCopy = { ...message };

    // Update messages state immediately for better UI responsiveness
    setMessages(prev => {
      const realActiveThreadId = activeThreadId || threadId;
      const messageThreadId = messageCopy.threadId;

      // SEMPRE adicionar se for uma resposta do assistente, independente do thread ativo
      const isAssistantResponse = messageCopy.role === 'assistant';
      const shouldAdd = isAssistantResponse || (messageThreadId === realActiveThreadId);

      if (!shouldAdd) {
        return prev;
      }

      // Verificar se a mensagem já existe no array para evitar duplicatas
      const messageExists = prev.some(
        m => m.role === messageCopy.role &&
             m.content === messageCopy.content &&
             Math.abs(m.timestamp.getTime() - messageCopy.timestamp.getTime()) < 1000
      );

      if (messageExists) {
        return prev;
      }

      return [...prev, messageCopy];
    });

    try {
      // OTIMIZAÇÃO: Usar função RPC para inserção mais eficiente
      const { data: insertResult, error } = await supabase
        .rpc('insert_chat_message', {
          p_user_id: user?.id,
          p_role: message.role,
          p_content: message.content,
          p_thread_id: threadId,
          p_image_url: message.image_url || null,
          p_file_url: message.file_url || null,
          p_file_type: message.file_type || null
        });

      if (error) {
        // Fallback para inserção manual se RPC falhar
        console.warn('RPC insert failed, using fallback:', error);
        const metadata: ChatMetadata & { image_url?: string; file_url?: string; file_type?: string } = { threadId };
        if (message.image_url) metadata.image_url = message.image_url;
        if (message.file_url) metadata.file_url = message.file_url;
        if (message.file_type) metadata.file_type = message.file_type;

        const { error: fallbackError } = await supabase
          .from('pedbook_chat_history')
          .insert({
            user_id: user?.id,
            role: message.role,
            content: message.content,
            metadata: metadata,
          });

        if (fallbackError) throw fallbackError;
      }



      // Se é uma resposta do assistente, atualizar a lista de threads para mostrar última mensagem
      if (message.role === 'assistant') {


        // Verificar se é o thread ativo atual
        const isActiveThread = threadId === activeThreadId; // Comparar com o thread realmente ativo

        // Atualizar o thread na lista com a nova última mensagem
        setThreads(prev => prev.map(thread =>
          thread.id === threadId
            ? {
                ...thread,
                lastMessage: message.content.substring(0, 100) + (message.content.length > 100 ? '...' : ''),
                createdAt: new Date(), // Atualizar para aparecer no topo
                hasNewResponse: !isActiveThread // Marcar como tendo nova resposta se não for o thread ativo
              }
            : thread
        ));

        // Forçar reload da lista para garantir ordem correta
        setTimeout(async () => {
          await loadThreads(true);
        }, 100);
      }

      // OTIMIZAÇÃO: Atualização inteligente de cache em vez de reload forçado
      if (isFirstMessageOfThread && message.role === 'user') {
        // Invalidar apenas o cache de threads para forçar reload na próxima consulta
        const cacheKey = `threads_${user?.id}`;
        setThreadsCache(prev => {
          const newCache = new Map(prev);
          newCache.delete(cacheKey);
          return newCache;
        });

        // Reload único e inteligente após um pequeno delay
        setTimeout(async () => {
          await loadThreads(true);
        }, 100);
      } else {
        // Para mensagens existentes, apenas invalidar cache do thread específico
        setMessagesCache(prev => {
          const newCache = new Map(prev);
          newCache.delete(threadId);
          return newCache;
        });
      }



      // Atualizar a lista de threads, mas não recarregar as mensagens
      // para evitar o efeito de piscar na interface
      // await loadMessages(threadId);
    } catch (error) {
      toast({
        title: "Erro ao salvar mensagem",
        description: "Ocorreu um erro ao salvar sua mensagem.",
        variant: "destructive",
      });
    } finally {
      if (message.role === 'assistant') {
        setIsLoading(false);
      }
    }
  };

  const createNewThread = () => {
    // Marcar que estamos criando um thread
    setIsCreatingThread(true);

    const newThread = {
      id: crypto.randomUUID(),
      title: 'Nova conversa',
      lastMessage: '',
      createdAt: new Date(),
    };





    // Update threads state immediately - CRÍTICO para aparecer na UI
    setThreads(prev => {
      // Verificar se o thread já existe para evitar duplicatas
      const threadExists = prev.some(t => t.id === newThread.id);
      if (threadExists) {

        return prev;
      }

      const newThreads = [newThread, ...prev];


      // Forçar re-render do componente pai IMEDIATAMENTE
      setTimeout(() => {
        setForceUpdate(prev => prev + 1);
      }, 0);

      // CRÍTICO: Forçar re-render adicional para garantir que aparece
      setTimeout(() => {
        setForceUpdate(prev => prev + 1);
      }, 100);

      return newThreads;
    });

    // Clear messages for the new thread
    setMessages([]);

    // Set loading to false to ensure UI is responsive
    setIsLoading(false);

    // Limpar cache para forçar reload quando necessário
    const cacheKey = `threads_${user?.id}`;
    setThreadsCache(prev => {
      const newCache = new Map(prev);
      newCache.delete(cacheKey);
  
      return newCache;
    });



    // Desmarcar criação após um tempo
    setTimeout(() => {
      setIsCreatingThread(false);
    }, 2000);

    return newThread;
  };

  const renameThread = async (threadId: string, newTitle: string) => {
    setThreads(prev =>
      prev.map(thread =>
        thread.id === threadId
          ? { ...thread, title: newTitle }
          : thread
      )
    );
  };

  const deleteThread = async (threadId: string) => {
    try {
      const { error } = await supabase
        .from('pedbook_chat_history')
        .delete()
        .eq('metadata->>threadId', threadId)
        .eq('user_id', user?.id);

      if (error) throw error;

      setThreads(prev => prev.filter(thread => thread.id !== threadId));
      if (threadId === activeThreadId) {
        setMessages([]);
      }
    } catch (error) {
      toast({
        title: "Erro ao excluir conversa",
        description: "Ocorreu um erro ao excluir a conversa.",
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    if (user?.id) {
      console.log(`🔄 [ChatHistory] Iniciando carregamento de threads para usuário: ${user.id}`);
      // Limpar cache para garantir dados frescos
      setThreadsCache(new Map());
      setMessagesCache(new Map());
      loadThreads(true); // Forçar reload
    } else {
      console.log(`⚠️ [ChatHistory] Usuário não autenticado`);
      // Limpar estado quando não há usuário
      setThreads([]);
      setMessages([]);
    }
  }, [loadThreads, user?.id]);

  useEffect(() => {
    if (activeThreadId && user?.id) {
      // CRÍTICO: Limpar mensagens IMEDIATAMENTE ao mudar thread para evitar mistura
      setMessages([]);

      // Carregar mensagens do novo thread
      loadMessages(activeThreadId);

      // Limpar indicador de nova resposta quando acessar o thread
      setThreads(prev => prev.map(thread =>
        thread.id === activeThreadId
          ? { ...thread, hasNewResponse: false }
          : thread
      ));
    } else if (!activeThreadId) {
      // Só limpar mensagens se activeThreadId for explicitamente null/undefined
      // Não limpar durante mudanças temporárias
      setMessages([]);
    }
  }, [activeThreadId, loadMessages, user?.id]);

  // Função para carregar mais threads (paginação)
  const loadMoreThreads = useCallback(async () => {
    if (!hasMoreThreads || isLoadingMore) return;

    setIsLoadingMore(true);
    try {
      const nextPage = threadsPage + 1;

      // Tentar RPC primeiro, com fallback se falhar
      let moreThreads = null;
      let rpcError = null;

      try {
        const { data, error } = await supabase
          .rpc('get_user_chat_threads', {
            p_user_id: user?.id,
            p_limit: 20,
            p_offset: nextPage * 20
          });

        if (error) {
          rpcError = error;
        } else {
          moreThreads = data;
        }
      } catch (err) {
        rpcError = err;
      }

      if (rpcError || !moreThreads) {
        console.warn('RPC loadMoreThreads failed, using fallback:', rpcError);
        // Para paginação, se RPC falhar, não temos como fazer fallback eficiente
        setHasMoreThreads(false);
        return;
      }

      if (moreThreads && moreThreads.length > 0) {
        const newThreads: Thread[] = moreThreads.map((thread: any) => ({
          id: thread.thread_id,
          title: thread.title,
          lastMessage: thread.last_message,
          createdAt: new Date(thread.last_message_date),
        }));

        setThreads(prev => [...prev, ...newThreads]);
        setThreadsPage(nextPage);

        if (moreThreads.length < 20) {
          setHasMoreThreads(false);
        }
      } else {
        setHasMoreThreads(false);
      }
    } catch (error) {
      console.error('Erro ao carregar mais threads:', error);
      toast({
        title: "Erro ao carregar mais conversas",
        description: "Ocorreu um erro ao carregar conversas adicionais.",
        variant: "destructive",
      });
    } finally {
      setIsLoadingMore(false);
    }
  }, [hasMoreThreads, isLoadingMore, threadsPage, user?.id, toast]);

  return {
    messages,
    setMessages,
    threads,
    isLoading,
    setIsLoading,
    saveMessage,
    createNewThread,
    renameThread,
    deleteThread,
    deleteAllThreads,
    forceUpdate,
    // Novas funcionalidades de paginação
    hasMoreThreads,
    isLoadingMore,
    loadMoreThreads,
  };
};
