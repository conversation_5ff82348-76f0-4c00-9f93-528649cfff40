import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { MessageSquarePlus, MoreVertical, Pencil, Trash2 } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface Thread {
  id: string;
  title: string;
  lastMessage: string;
  createdAt: Date;
  hasNewResponse?: boolean;
}

interface ThreadListProps {
  threads: Thread[];
  activeThreadId?: string;
  onThreadSelect: (threadId: string) => void;
  onNewThread: (resetOnly?: boolean) => void;
  onRenameThread: (threadId: string, newTitle: string) => void;
  onDeleteThread: (threadId: string) => void;
  onDeleteAllThreads: () => void;
  isLoading?: boolean; // Para bloquear navegação durante carregamento
  hasMoreThreads?: boolean;
  isLoadingMore?: boolean;
  onLoadMoreThreads?: () => void;
}

export const ThreadList: React.FC<ThreadListProps> = ({
  threads,
  activeThreadId,
  onThreadSelect,
  onNewThread,
  onRenameThread,
  onDeleteThread,
  onDeleteAllThreads,
  isLoading = false,
  hasMoreThreads = false,
  isLoadingMore = false,
  onLoadMoreThreads,
}) => {
  // Debug log para verificar threads
  React.useEffect(() => {
    console.log(`📋 [ThreadList] Threads recebidos: ${threads.length}`, threads);
  }, [threads]);
  const [renameDialogOpen, setRenameDialogOpen] = React.useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  const [deleteAllDialogOpen, setDeleteAllDialogOpen] = React.useState(false);
  const [threadToManage, setThreadToManage] = React.useState<string | null>(null);
  const [newTitle, setNewTitle] = React.useState("");



  const handleRename = (threadId: string) => {
    if (newTitle.trim()) {
      onRenameThread(threadId, newTitle.trim());
      setRenameDialogOpen(false);
      setNewTitle("");
      setThreadToManage(null);
    }
  };

  const handleDelete = (threadId: string) => {
    onDeleteThread(threadId);
    setDeleteDialogOpen(false);
    setThreadToManage(null);
  };

  const openRenameDialog = (thread: Thread) => {
    setThreadToManage(thread.id);
    setNewTitle(thread.title);
    setRenameDialogOpen(true);
  };

  const openDeleteDialog = (threadId: string) => {
    setThreadToManage(threadId);
    setDeleteDialogOpen(true);
  };

  return (
    <div className="w-72 border-r border-gray-200 dark:border-slate-700/50 bg-gray-50 dark:bg-slate-800/50 flex flex-col h-full shadow-lg">
      <div className="p-4 border-b border-gray-200 dark:border-slate-700/50 bg-white dark:bg-slate-800">
        <Button
          onClick={() => {
            onNewThread(); // CORREÇÃO: Criar nova thread, não apenas limpar
          }}
          className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-md"
          disabled={isLoading}
        >
          <MessageSquarePlus className="w-4 h-4 mr-2" />
          Nova Conversa
        </Button>
      </div>

      <div className="px-4 py-3 bg-gray-100/80 dark:bg-slate-700/30 border-b border-gray-200 dark:border-slate-700/50">
        <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
          Histórico de conversas
          {isLoading && <span className="ml-2 text-xs text-blue-500">(aguardando resposta...)</span>}
        </h3>
      </div>

      <ScrollArea className="flex-1">
        <div className="space-y-1 p-2">
          {threads.length === 0 ? (
            <div className="text-center py-8 px-4">
              <p className="text-sm text-gray-500 dark:text-gray-400 italic">
                Nenhuma conversa encontrada
              </p>
            </div>
          ) : (
            <>
              {threads.map((thread) => (
                <div
                  key={thread.id}
                  className={`flex items-center group rounded-lg transition-all duration-200 ${
                    activeThreadId === thread.id
                      ? 'bg-blue-50 dark:bg-blue-900/20 border-l-2 border-blue-500'
                      : 'hover:bg-gray-100 dark:hover:bg-slate-700/30'
                  }`}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start text-left pr-2 rounded-lg relative ${
                      activeThreadId === thread.id
                        ? 'text-blue-700 dark:text-blue-300'
                        : 'text-gray-700 dark:text-gray-300'
                    } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                    onClick={() => {
                      if (!isLoading) {
                        onThreadSelect(thread.id);
                      }
                    }}
                    disabled={isLoading}
                  >
                    <div className="w-full overflow-hidden py-1">
                      <div className="flex items-center gap-2">
                        <div className="font-medium truncate flex-1">{thread.title}</div>
                        {thread.hasNewResponse && activeThreadId !== thread.id && (
                          <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></div>
                        )}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400 truncate mt-1">
                        {new Date(thread.createdAt).toLocaleDateString()}
                      </div>
                    </div>
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="w-8 h-8 opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-48">
                      <DropdownMenuItem onClick={() => openRenameDialog(thread)}>
                        <Pencil className="w-4 h-4 mr-2" />
                        Renomear
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="text-red-600 dark:text-red-400"
                        onClick={() => openDeleteDialog(thread.id)}
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        Excluir
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              ))}

              {/* Botão para carregar mais threads */}
              {hasMoreThreads && onLoadMoreThreads && (
                <div className="px-2 py-2">
                  <Button
                    variant="outline"
                    className="w-full text-sm"
                    onClick={onLoadMoreThreads}
                    disabled={isLoadingMore}
                  >
                    {isLoadingMore ? (
                      <>
                        <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin mr-2"></div>
                        Carregando...
                      </>
                    ) : (
                      'Carregar mais conversas'
                    )}
                  </Button>
                </div>
              )}
            </>
          )}
        </div>
      </ScrollArea>

      <div className="p-4 border-t border-gray-200 dark:border-slate-700/50 bg-gray-50 dark:bg-slate-800/80">
        <AlertDialog open={deleteAllDialogOpen} onOpenChange={setDeleteAllDialogOpen}>
          <AlertDialogTrigger asChild>
            <Button
              variant="outline"
              className="w-full text-red-600 dark:text-red-400 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 border-red-200 dark:border-red-900/30"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Limpar todas conversas
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent className="border-red-100 dark:border-red-900/30">
            <AlertDialogHeader>
              <AlertDialogTitle className="text-red-600 dark:text-red-400">Excluir todas as conversas</AlertDialogTitle>
              <AlertDialogDescription>
                Tem certeza que deseja excluir todas as conversas? Esta ação não pode ser desfeita.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancelar</AlertDialogCancel>
              <AlertDialogAction
                onClick={onDeleteAllThreads}
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                Excluir todas
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>

      <Dialog open={renameDialogOpen} onOpenChange={setRenameDialogOpen}>
        <DialogContent className="border-blue-100 dark:border-blue-900/30">
          <DialogHeader>
            <DialogTitle className="text-blue-600 dark:text-blue-400">Renomear Conversa</DialogTitle>
            <DialogDescription>
              Digite o novo nome para esta conversa
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <Input
              value={newTitle}
              onChange={(e) => setNewTitle(e.target.value)}
              placeholder="Novo título"
              className="w-full border-blue-200 dark:border-blue-900/30 focus:ring-blue-500"
              autoFocus
            />
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setRenameDialogOpen(false)}
                className="border-gray-200 dark:border-gray-700"
              >
                Cancelar
              </Button>
              <Button
                onClick={() => threadToManage && handleRename(threadToManage)}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                Salvar
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent className="border-red-100 dark:border-red-900/30">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-red-600 dark:text-red-400">Excluir Conversa</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir esta conversa? Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="border-gray-200 dark:border-gray-700">Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => threadToManage && handleDelete(threadToManage)}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              Excluir
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};