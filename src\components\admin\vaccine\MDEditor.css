/* MDEditor Styles */
@import '@uiw/react-md-editor/markdown-editor.css';

/* Dark mode support */
[data-color-mode*='dark'] .w-md-editor {
  background-color: hsl(var(--background));
  border-color: hsl(var(--border));
}

[data-color-mode*='dark'] .w-md-editor-text-textarea,
[data-color-mode*='dark'] .w-md-editor-text-input,
[data-color-mode*='dark'] .w-md-editor-text {
  background-color: hsl(var(--background)) !important;
  color: hsl(var(--foreground)) !important;
  border-color: hsl(var(--border));
}

[data-color-mode*='dark'] .w-md-editor-toolbar {
  background-color: hsl(var(--muted));
  border-color: hsl(var(--border));
}

[data-color-mode*='dark'] .w-md-editor-toolbar-divider {
  background-color: hsl(var(--border));
}

/* Light mode adjustments */
[data-color-mode*='light'] .w-md-editor {
  background-color: hsl(var(--background));
  border-color: hsl(var(--border));
}

[data-color-mode*='light'] .w-md-editor-text-textarea,
[data-color-mode*='light'] .w-md-editor-text-input,
[data-color-mode*='light'] .w-md-editor-text {
  background-color: hsl(var(--background)) !important;
  color: hsl(var(--foreground)) !important;
  border-color: hsl(var(--border));
}

/* Toolbar styling */
.w-md-editor-toolbar {
  padding: 8px;
  border-radius: 6px 6px 0 0;
}

.w-md-editor-toolbar button {
  border-radius: 4px;
  transition: all 0.2s;
}

.w-md-editor-toolbar button:hover {
  background-color: hsl(var(--accent));
}

/* Editor content area */
.w-md-editor-text {
  border-radius: 0 0 6px 6px;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: 14px;
  line-height: 1.5;
}

/* Focus styles */
.w-md-editor:focus-within {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* Placeholder styling */
.w-md-editor-text-textarea::placeholder {
  color: hsl(var(--muted-foreground));
  opacity: 0.7;
}

/* Custom scrollbar */
.w-md-editor-text-textarea::-webkit-scrollbar {
  width: 8px;
}

.w-md-editor-text-textarea::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 4px;
}

.w-md-editor-text-textarea::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground));
  border-radius: 4px;
}

.w-md-editor-text-textarea::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--foreground));
}
