import React, { useState } from 'react';
import { GraduationCap, Target, BookOpen, Users, Award, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

const ResidencyBeta = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    currentStatus: '',
    medicalSchoolYear: '',
    targetResidency: '',
    studyPreferences: [],
    timeAvailable: '',
    howFoundPedbook: ''
  });

  const features = [
    {
      icon: Target,
      title: "Simulados Personalizados",
      description: "Questões adaptadas ao seu nível e especialidade desejada"
    },
    {
      icon: BookOpen,
      title: "Conteúdo Exclusivo",
      description: "Material atualizado com as últimas diretrizes e protocolos"
    },
    {
      icon: Users,
      title: "Mentoria Especializada",
      description: "Acompanhamento com residentes aprovados e especialistas"
    },
    {
      icon: Award,
      title: "Ranking Nacional",
      description: "Compare seu desempenho com outros candidatos"
    }
  ];

  const benefits = [
    "🎯 Acesso antecipado GRATUITO por 3 meses",
    "📚 E-book: 'Guia Completo de Residência Médica 2024'",
    "🏆 Simulados ilimitados personalizados",
    "💡 Mentoria individual com aprovados",
    "📊 Relatórios detalhados de performance",
    "🎪 Webinars exclusivos mensais"
  ];

  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-50 to-white dark:from-slate-900 dark:to-slate-800">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-4 py-2 rounded-full text-sm font-medium mb-6">
            <Clock className="h-4 w-4" />
            Lançamento em breve - Seja um dos primeiros!
          </div>
          
          <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text mb-6">
            Plataforma de Residência
          </h1>
          
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
            A mais completa plataforma de preparação para residência médica. 
            Desenvolvida por quem já passou e sabe o que funciona.
          </p>
          
          <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-500 dark:text-gray-400">
            <span>✅ +10.000 questões atualizadas</span>
            <span>✅ Simulados personalizados</span>
            <span>✅ Mentoria especializada</span>
            <span>✅ Conteúdo exclusivo</span>
          </div>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {features.map((feature, index) => (
            <Card key={index} className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <feature.icon className="h-12 w-12 mx-auto text-blue-600 dark:text-blue-400 mb-4" />
                <CardTitle className="text-lg">{feature.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 dark:text-gray-300">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Signup Form */}
        <div className="max-w-2xl mx-auto">
          <Card className="shadow-xl">
            <CardHeader className="text-center">
              <CardTitle className="text-2xl flex items-center justify-center gap-2">
                <GraduationCap className="h-6 w-6" />
                Cadastre-se para o Beta
              </CardTitle>
              <p className="text-gray-600 dark:text-gray-300">
                Seja um dos primeiros a testar nossa plataforma exclusiva
              </p>
            </CardHeader>
            
            <CardContent className="space-y-6">
              {/* Benefits List */}
              <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                <h3 className="font-semibold text-green-800 dark:text-green-300 mb-3">
                  🎁 Benefícios exclusivos para beta testers:
                </h3>
                <ul className="space-y-1 text-sm">
                  {benefits.map((benefit, index) => (
                    <li key={index} className="text-green-700 dark:text-green-300">{benefit}</li>
                  ))}
                </ul>
              </div>

              {/* Form Fields */}
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Nome completo *</Label>
                  <Input 
                    id="name" 
                    placeholder="Seu nome completo"
                    value={formData.name}
                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                  />
                </div>
                
                <div>
                  <Label htmlFor="email">E-mail *</Label>
                  <Input 
                    id="email" 
                    type="email" 
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={(e) => setFormData({...formData, email: e.target.value})}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="status">Situação atual *</Label>
                <Select value={formData.currentStatus} onValueChange={(value) => setFormData({...formData, currentStatus: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione sua situação atual" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="student">Estudante de medicina</SelectItem>
                    <SelectItem value="recent_graduate">Recém-formado</SelectItem>
                    <SelectItem value="resident">Já sou residente</SelectItem>
                    <SelectItem value="graduated">Médico formado</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="target">Especialidade de interesse *</Label>
                <Select value={formData.targetResidency} onValueChange={(value) => setFormData({...formData, targetResidency: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Qual especialidade você quer?" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pediatrics">Pediatria</SelectItem>
                    <SelectItem value="internal_medicine">Clínica Médica</SelectItem>
                    <SelectItem value="surgery">Cirurgia Geral</SelectItem>
                    <SelectItem value="gynecology">Ginecologia e Obstetrícia</SelectItem>
                    <SelectItem value="psychiatry">Psiquiatria</SelectItem>
                    <SelectItem value="other">Outra especialidade</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3">
                🚀 Quero participar do Beta!
              </Button>

              <p className="text-xs text-center text-gray-500 dark:text-gray-400">
                Ao se cadastrar, você concorda em receber atualizações sobre a plataforma. 
                Seus dados estão seguros conosco.
              </p>
            </CardContent>
          </Card>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default ResidencyBeta;
