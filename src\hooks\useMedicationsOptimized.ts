import { useQuery, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

// Tipos de dados otimizados
interface MedicationSummary {
  id: string;
  name: string;
  slug: string;
  description?: string;
  brands?: string[];
  category_id: string;
  category: {
    id: string;
    name: string;
    color?: string;
    icon_url?: string;
  };
  measure_types?: string;
  required_measures?: string[];
  contraindications?: string;
  guidelines?: string;
  scientific_references?: string;
  dosages_summary: Array<{
    id: string;
    name: string;
    summary?: string;
    age_group?: string;
    type?: string;
  }>;
  use_cases_summary: Array<{
    id: string;
    name: string;
    display_order?: number;
  }>;
  dosages_count: number;
  use_cases_count: number;
}

interface CategorySummary {
  id: string;
  name: string;
  color?: string;
  icon_url?: string;
  description?: string;
  medication_count: number;
}

interface MedicationsStructure {
  medications: MedicationSummary[];
  categories: CategorySummary[];
  stats: {
    total_medications: number;
    total_dosages: number;
    total_use_cases: number;
    avg_dosages_per_medication: number;
    categories_count: number;
  };
  last_updated: string;
}

interface SearchResult {
  id: string;
  name: string;
  slug: string;
  description?: string;
  brands?: string[];
  category_id: string;
  category: {
    id: string;
    name: string;
    color?: string;
  };
  dosages_count: number;
  search_rank: number;
}

// Cache strategies otimizadas
const MEDICATION_CACHE = {
  STRUCTURE: {
    staleTime: 2 * 60 * 60 * 1000, // 2 horas (dados estáticos)
    gcTime: 4 * 60 * 60 * 1000,    // 4 horas
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: true,
    retry: 2,
  },
  DETAILS: {
    staleTime: 30 * 60 * 1000,     // 30 minutos
    gcTime: 60 * 60 * 1000,       // 1 hora
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: true,
    retry: 2,
  },
  SEARCH: {
    staleTime: 10 * 60 * 1000,     // 10 minutos
    gcTime: 20 * 60 * 1000,       // 20 minutos
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    retry: 1,
  }
};

/**
 * Hook principal - estrutura completa otimizada
 * Usa view materializada para performance máxima
 */
export const useMedicationsStructure = () => {
  return useQuery({
    queryKey: ['medications-structure-optimized-v1'],
    queryFn: async (): Promise<MedicationsStructure> => {
      try {
        // Usar função otimizada com view materializada
        const { data, error } = await supabase.rpc('get_medications_structure_cached');
        
        if (error) {
          console.warn('Função otimizada falhou, usando fallback:', error);
          
          // Fallback para consulta direta se necessário
          const { data: fallbackData, error: fallbackError } = await supabase
            .from('pedbook_medications')
            .select(`
              id, name, slug, description, brands, category_id, measure_types,
              pedbook_medication_categories(id, name, color, icon_url)
            `)
            .order('name');
            
          if (fallbackError) throw fallbackError;
          
          // Estrutura de fallback simplificada
          return {
            medications: fallbackData?.map(med => ({
              ...med,
              category: med.pedbook_medication_categories || { id: '', name: 'Sem categoria' },
              dosages_summary: [],
              use_cases_summary: [],
              dosages_count: 0,
              use_cases_count: 0
            })) || [],
            categories: [],
            stats: {
              total_medications: fallbackData?.length || 0,
              total_dosages: 0,
              total_use_cases: 0,
              avg_dosages_per_medication: 0,
              categories_count: 0
            },
            last_updated: new Date().toISOString()
          };
        }
        
        return data as MedicationsStructure;
      } catch (err: any) {
        console.error('Erro ao buscar estrutura de medicamentos:', err);
        throw err;
      }
    },
    ...MEDICATION_CACHE.STRUCTURE,
  });
};

/**
 * Hook para busca otimizada de medicamentos
 * Usa full-text search com ranking
 */
export const useMedicationSearch = (searchQuery: string, categoryId?: string) => {
  return useQuery({
    queryKey: ['medication-search-optimized', searchQuery, categoryId],
    queryFn: async (): Promise<SearchResult[]> => {
      if (!searchQuery || searchQuery.length < 2) return [];
      
      try {
        // Usar função de busca otimizada
        const { data, error } = await supabase.rpc('search_medications_optimized', {
          search_query: searchQuery,
          category_filter: categoryId || null,
          limit_results: 20
        });
        
        if (error) {
          console.warn('Busca otimizada falhou, usando fallback:', error);
          
          // Fallback para busca ILIKE
          let query = supabase
            .from('pedbook_medications')
            .select(`
              id, name, slug, description, brands, category_id,
              pedbook_medication_categories(id, name, color)
            `)
            .ilike('name', `%${searchQuery}%`)
            .limit(20);
            
          if (categoryId) {
            query = query.eq('category_id', categoryId);
          }
          
          const { data: fallbackData, error: fallbackError } = await query;
          if (fallbackError) throw fallbackError;
          
          return fallbackData?.map(med => ({
            ...med,
            category: med.pedbook_medication_categories || { id: '', name: 'Sem categoria' },
            dosages_count: 0,
            search_rank: 0.5
          })) || [];
        }
        
        return data || [];
      } catch (err: any) {
        console.error('Erro na busca de medicamentos:', err);
        throw err;
      }
    },
    enabled: searchQuery.length >= 2,
    ...MEDICATION_CACHE.SEARCH,
  });
};

/**
 * Hook para categorias apenas (derivado da estrutura)
 */
export const useMedicationCategories = () => {
  const { data: structure, isLoading, error } = useMedicationsStructure();
  
  return {
    data: structure?.categories || [],
    isLoading,
    error
  };
};

/**
 * Hook para lista de medicamentos apenas (derivado da estrutura)
 */
export const useMedicationsList = () => {
  const { data: structure, isLoading, error } = useMedicationsStructure();
  
  return {
    data: structure?.medications || [],
    isLoading,
    error
  };
};

/**
 * Hook para estatísticas (derivado da estrutura)
 */
export const useMedicationsStats = () => {
  const { data: structure, isLoading, error } = useMedicationsStructure();
  
  return {
    data: structure?.stats || null,
    isLoading,
    error
  };
};

/**
 * Hook para prefetch inteligente
 */
export const useMedicationPrefetch = () => {
  const queryClient = useQueryClient();
  
  const prefetchMedicationDetails = (slug: string) => {
    queryClient.prefetchQuery({
      queryKey: ['medication-details', slug],
      queryFn: async () => {
        const { data, error } = await supabase
          .from('pedbook_medications')
          .select(`
            *,
            pedbook_medication_categories(id, name, slug, color),
            pedbook_medication_use_cases(
              id, name, description, display_order,
              pedbook_medication_dosages(
                id, name, type, summary, description, 
                dosage_template, age_group
              )
            )
          `)
          .eq('slug', slug)
          .maybeSingle();
          
        if (error && error.code !== 'PGRST116') throw error;
        return data;
      },
      ...MEDICATION_CACHE.DETAILS,
    });
  };
  
  return { prefetchMedicationDetails };
};

/**
 * Hook para invalidação de cache
 */
export const useMedicationCacheInvalidation = () => {
  const queryClient = useQueryClient();
  
  const invalidateAll = () => {
    queryClient.invalidateQueries({ queryKey: ['medications'] });
    queryClient.invalidateQueries({ queryKey: ['medication'] });
  };
  
  const invalidateStructure = () => {
    queryClient.invalidateQueries({ queryKey: ['medications-structure-optimized-v1'] });
  };
  
  const invalidateSearch = () => {
    queryClient.invalidateQueries({ queryKey: ['medication-search-optimized'] });
  };
  
  return {
    invalidateAll,
    invalidateStructure,
    invalidateSearch,
  };
};
