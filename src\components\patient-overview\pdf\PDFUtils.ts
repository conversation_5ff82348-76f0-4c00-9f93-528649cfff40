import { formatAgeMedical } from '@/utils/formatAge';

/**
 * Utilitários para geração de PDF
 */
export class PDFUtils {
  /**
   * Limpa texto removendo acentos e caracteres especiais
   */
  static cleanText(text: string): string {
    return text
      .replace(/[áàâãä]/g, 'a')
      .replace(/[éèêë]/g, 'e')
      .replace(/[íìîï]/g, 'i')
      .replace(/[óòôõö]/g, 'o')
      .replace(/[úùûü]/g, 'u')
      .replace(/[ç]/g, 'c')
      .replace(/[ÁÀÂÃÄ]/g, 'A')
      .replace(/[ÉÈÊË]/g, 'E')
      .replace(/[ÍÌÎÏ]/g, 'I')
      .replace(/[ÓÒÔÕÖ]/g, 'O')
      .replace(/[ÚÙÛÜ]/g, 'U')
      .replace(/[Ç]/g, 'C');
  }

  /**
   * Formata idade para exibição no PDF
   */
  static formatAge(ageInMonths: number, useCorrectedAge: boolean): string {
    return formatAgeMedical(ageInMonths, useCorrectedAge);
  }

  /**
   * Formata peso em gramas
   */
  static formatWeight(weightInKg: number): string {
    return `${(weightInKg * 1000).toFixed(0)}g`;
  }

  /**
   * Formata altura em centímetros
   */
  static formatHeight(heightInCm: number): string {
    return `${heightInCm} cm`;
  }

  /**
   * Formata idade gestacional
   */
  static formatGestationalAge(gestationalAge: number): string {
    const weeks = Math.floor(gestationalAge);
    const days = Math.round((gestationalAge % 1) * 7);
    return `${weeks} semanas e ${days} dias`;
  }

  /**
   * Determina maturidade baseada na idade gestacional
   */
  static getMaturidade(gestationalAge: number): string {
    return gestationalAge >= 37 ? 'A termo' : 'Pre-termo';
  }

  /**
   * Gera nome de arquivo para o PDF
   */
  static generateFileName(patientName?: string): string {
    const cleanName = patientName 
      ? this.cleanText(patientName).replace(/\s+/g, '_')
      : 'paciente';
    const date = new Date().toISOString().split('T')[0];
    return `laudo_pediatrico_${cleanName}_${date}.pdf`;
  }

  /**
   * Formata data e hora atual
   */
  static formatDateTime(): string {
    const now = new Date();
    const date = now.toLocaleDateString('pt-BR');
    const time = now.toLocaleTimeString('pt-BR');
    return `${date} | ${time}`;
  }



  /**
   * Carrega logo como base64 com tratamento de transparência
   */
  static async loadLogo(): Promise<string> {
    try {
      const response = await fetch('/faviconx.webp');
      const blob = await response.blob();

      // Converter para canvas para remover fundo preto e melhorar transparência
      const img = new Image();
      img.crossOrigin = 'anonymous';

      return new Promise((resolve) => {
        img.onload = () => {
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');

          canvas.width = img.width;
          canvas.height = img.height;

          // Desenhar com fundo transparente
          ctx?.clearRect(0, 0, canvas.width, canvas.height);
          ctx?.drawImage(img, 0, 0);

          // Converter para base64 PNG (melhor transparência)
          resolve(canvas.toDataURL('image/png'));
        };

        img.onerror = () => {
          // Fallback para método original
          const reader = new FileReader();
          reader.onload = () => resolve(reader.result as string);
          reader.readAsDataURL(blob);
        };

        img.src = URL.createObjectURL(blob);
      });
    } catch (error) {
      return '';
    }
  }
}
