// Configurações centralizadas para geração de PDF
export const PDF_CONFIG = {
  // Configurações da página
  page: {
    width: 210, // A4 width in mm
    height: 297, // A4 height in mm
    margin: 20, // Margem em mm
    lineHeight: 6
  },

  // Configurações de fonte
  fonts: {
    title: 18,
    section: 14,
    normal: 10,
    small: 9,
    tiny: 8
  },

  // Cores padronizadas
  colors: {
    primary: [30, 64, 175] as [number, number, number], // Azul principal
    text: [0, 0, 0] as [number, number, number], // Texto preto
    gray: [100, 100, 100] as [number, number, number], // Texto cinza
    
    // Cores para cards
    success: [240, 253, 244] as [number, number, number], // Verde claro
    warning: [255, 251, 235] as [number, number, number], // Amarelo claro
    error: [254, 242, 242] as [number, number, number], // Vermelho claro
    info: [239, 246, 255] as [number, number, number], // Azul claro
    purple: [250, 245, 255] as [number, number, number] // Roxo claro
  },

  // Configurações do logo
  logo: {
    size: 120, // Tamanho em mm
    opacity: 0.08, // Opacidade da marca d'água
    offsetY: -10 // Offset vertical
  },

  // Textos padrão
  texts: {
    title: 'LAUDO PEDIATRICO COMPLETO',
    subtitle: 'PedBook - Analise Antropometrica, Nutricional, Vacinal e de Desenvolvimento',
    footer: 'Este laudo foi gerado automaticamente pelo PedBook e deve ser interpretado por profissional de saude qualificado.',
    
    // Seções
    sections: {
      patient: 'DADOS DO PACIENTE',
      perinatal: 'DADOS PERINATAIS', 
      analysis: 'ANALISE ANTROPOMETRICA',
      supplementation: 'RECOMENDACOES DE SUPLEMENTACAO',
      vaccines: 'CALENDARIO VACINAL (SUS)'
    },

    // Ícones textuais
    icons: {
      patient: '[PACIENTE]',
      perinatal: '[NASCIMENTO]',
      analysis: '[MEDIDAS]',
      supplementation: '[VITAMINAS]',
      vaccines: '[VACINAS]'
    }
  }
};

// Tipos para melhor tipagem
export interface PDFPageConfig {
  width: number;
  height: number;
  margin: number;
  lineHeight: number;
}

export interface PDFColors {
  primary: [number, number, number];
  text: [number, number, number];
  gray: [number, number, number];
  success: [number, number, number];
  warning: [number, number, number];
  error: [number, number, number];
  info: [number, number, number];
  purple: [number, number, number];
}

export interface PDFTexts {
  title: string;
  subtitle: string;
  footer: string;
  sections: {
    patient: string;
    perinatal: string;
    analysis: string;
    supplementation: string;
    vaccines: string;
  };
  icons: {
    patient: string;
    perinatal: string;
    analysis: string;
    supplementation: string;
    vaccines: string;
  };
}
