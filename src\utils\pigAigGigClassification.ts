// Classificação PIG/AIG/GIG baseada nas curvas INTERGROWTH-21st e OMS
// Dados atualizados e precisos para classificação neonatal

export interface BirthWeightClassification {
  classification: "PIG" | "AIG" | "GIG";
  percentile: number;
  description: string;
  color: string;
  clinicalNote?: string;
}

// Tabela baseada em INTERGROWTH-21st e OMS (valores em gramas)
// Percentis 10 e 90 são os pontos de corte para PIG/AIG/GIG
const BIRTH_WEIGHT_PERCENTILES = {
  // Semana gestacional: [P10, P50, P90]
  24: [515, 670, 870],
  25: [580, 750, 980],
  26: [650, 840, 1100],
  27: [730, 940, 1230],
  28: [820, 1050, 1370],
  29: [920, 1170, 1530],
  30: [1030, 1300, 1700],
  31: [1150, 1440, 1880],
  32: [1280, 1590, 2070],
  33: [1430, 1750, 2270],
  34: [1590, 1920, 2480],
  35: [1760, 2100, 2690],
  36: [1940, 2280, 2900],
  37: [2130, 2460, 3110],
  38: [2320, 2640, 3320],
  39: [2510, 2820, 3530],
  40: [2700, 3000, 3730],
  41: [2880, 3170, 3920],
  42: [3050, 3330, 4100],
};

// Função para interpolar entre idades gestacionais
function interpolatePercentiles(ga: number): [number, number, number] {
  const gaFloor = Math.floor(ga);
  const gaCeil = Math.ceil(ga);

  // Se é idade exata, usar valores diretos
  if (gaFloor === gaCeil) {
    const clampedGA = Math.max(24, Math.min(42, gaFloor));
    return BIRTH_WEIGHT_PERCENTILES[clampedGA as keyof typeof BIRTH_WEIGHT_PERCENTILES] || [2700, 3000, 3730];
  }

  // Interpolar entre duas idades
  const lowerGA = Math.max(24, Math.min(42, gaFloor));
  const upperGA = Math.max(24, Math.min(42, gaCeil));

  const lowerPercentiles = BIRTH_WEIGHT_PERCENTILES[lowerGA as keyof typeof BIRTH_WEIGHT_PERCENTILES];
  const upperPercentiles = BIRTH_WEIGHT_PERCENTILES[upperGA as keyof typeof BIRTH_WEIGHT_PERCENTILES];

  if (!lowerPercentiles || !upperPercentiles) {
    return [2700, 3000, 3730]; // Valores padrão para termo
  }

  const ratio = ga - gaFloor;

  return [
    lowerPercentiles[0] + (upperPercentiles[0] - lowerPercentiles[0]) * ratio,
    lowerPercentiles[1] + (upperPercentiles[1] - lowerPercentiles[1]) * ratio,
    lowerPercentiles[2] + (upperPercentiles[2] - lowerPercentiles[2]) * ratio,
  ];
}

export function calculateBirthWeightClassification(
  birthWeightGrams: number,
  gestationalAgeWeeks: number
): BirthWeightClassification {
  // Validação de entrada
  if (!birthWeightGrams || birthWeightGrams < 300 || birthWeightGrams > 6000) {
    throw new Error(`Peso ao nascer inválido: ${birthWeightGrams}g. Deve estar entre 300g e 6000g.`);
  }

  if (!gestationalAgeWeeks || gestationalAgeWeeks < 22 || gestationalAgeWeeks > 44) {
    throw new Error(`Idade gestacional inválida: ${gestationalAgeWeeks} semanas. Deve estar entre 22 e 44 semanas.`);
  }

  // Usar interpolação para maior precisão
  const [p10, p50, p90] = interpolatePercentiles(gestationalAgeWeeks);

  // Calcular percentil aproximado
  let percentile: number;
  let classification: "PIG" | "AIG" | "GIG";
  let description: string;
  let color: string;
  let clinicalNote: string;

  if (birthWeightGrams < p10) {
    // PIG - Pequeno para idade gestacional (< P10)
    // Cálculo mais preciso para percentis baixos
    const ratio = birthWeightGrams / p10;
    if (ratio < 0.5) {
      percentile = Math.max(0.1, ratio * 2); // P0.1 a P1
    } else {
      percentile = Math.max(1, ratio * 10); // P1 a P10
    }
    classification = "PIG";
    description = "Pequeno para idade gestacional";
    color = "red";
    clinicalNote = "Risco aumentado de hipoglicemia, hipotermia e RCIU. Avaliação médica necessária.";
  } else if (birthWeightGrams > p90) {
    // GIG - Grande para idade gestacional (> P90)
    // Cálculo mais preciso para percentis altos
    const excessRatio = (birthWeightGrams - p90) / (p90 * 0.2);
    percentile = Math.min(99.9, 90 + excessRatio * 9);
    classification = "GIG";
    description = "Grande para idade gestacional";
    color = "orange";
    clinicalNote = "Associado a diabetes materno, risco de parto traumático e hipoglicemia neonatal.";
  } else {
    // AIG - Adequado para idade gestacional (P10-P90)
    if (birthWeightGrams <= p50) {
      percentile = 10 + ((birthWeightGrams - p10) / (p50 - p10)) * 40;
    } else {
      percentile = 50 + ((birthWeightGrams - p50) / (p90 - p50)) * 40;
    }
    classification = "AIG";
    description = "Adequado para idade gestacional";
    color = "green";
    clinicalNote = "Peso adequado para a idade gestacional. Crescimento normal.";
  }

  return {
    classification,
    percentile: Math.round(percentile),
    description,
    color,
    clinicalNote
  };
}

export function formatBirthWeightClassification(classification: BirthWeightClassification): string {
  return `${classification.classification} (P${classification.percentile})`;
}

// Função para obter emoji baseado na classificação
export function getClassificationEmoji(classification: "PIG" | "AIG" | "GIG"): string {
  switch (classification) {
    case "PIG":
      return "🔴";
    case "AIG":
      return "🟢";
    case "GIG":
      return "🟠";
    default:
      return "⚪";
  }
}

// Função para obter cor Tailwind baseada na classificação
export function getClassificationColor(classification: "PIG" | "AIG" | "GIG"): string {
  switch (classification) {
    case "PIG":
      return "text-red-600 bg-red-50 dark:bg-red-900/20 dark:text-red-400";
    case "AIG":
      return "text-green-600 bg-green-50 dark:bg-green-900/20 dark:text-green-400";
    case "GIG":
      return "text-orange-600 bg-orange-50 dark:bg-orange-900/20 dark:text-orange-400";
    default:
      return "text-gray-600 bg-gray-50 dark:bg-gray-900/20 dark:text-gray-400";
  }
}
